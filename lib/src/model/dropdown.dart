import 'package:driver/src/app.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';

class SelectorLabel {
  final String label;
  SelectorLabel(this.label);
}

class OriginLabel extends SelectorLabel {
  final String country;
  final String city;
  OriginLabel(this.country, this.city)
      : super(appLoc(MyApp.materialKey.currentContext!)
            .loadFrom('${localizedString(country)}, ${localizedString(city)}'));
}

class DestLabel extends SelectorLabel {
  final String country;
  DestLabel(this.country) : super(localizedString(country));
}

class CountryCode extends SelectorLabel {
  final String name;
  final String code;
  final String areaCode;
  final int length;

  CountryCode(this.name, this.code, this.areaCode, this.length)
      : super(localizedString(name));

  static CountryCode fromMap(Map<String, dynamic> map) {
    return CountryCode(
        map['name'], map['code'], map['areaCode'], map['length']);
  }

  static List<DropdownMenuEntry<CountryCode>> get entries {
    return SettingsController.shared.countryAreaCodes
        .map((label) => DropdownMenuEntry<CountryCode>(
            value: label,
            label: '${label.label} (${label.areaCode})',
            leadingIcon: Image.asset(
              'assets/images/country/${label.code}.png',
              width: 24,
              fit: BoxFit.fitWidth,
            ),
            style: MenuItemButton.styleFrom(backgroundColor: Colors.white)))
        .toList();
  }
}

class QuoteTruckTypeLabel extends SelectorLabel {
  final String? type;
  QuoteTruckTypeLabel(this.type)
      : super(localizedString(type ??
            appLoc(MyApp.materialKey.currentContext!).allQuoteTruckTypes));
}

class QuoteAreaLabel extends SelectorLabel {
  final String? area;
  QuoteAreaLabel(this.area)
      : super(localizedString(
            area ?? appLoc(MyApp.materialKey.currentContext!).allQuoteAreas));
}

class TruckType extends SelectorLabel {
  final String name;
  final String img;

  TruckType(this.name, this.img) : super(localizedString(name));

  static TruckType fromMap(Map<String, dynamic> map) {
    return TruckType(map['name'], map['img']);
  }
}

class TonnageLabel extends SelectorLabel {
  final String tonnage;
  TonnageLabel(this.tonnage) : super(localizedString(tonnage));
}

class VolumeLabel extends SelectorLabel {
  final String volume;
  VolumeLabel(this.volume) : super(localizedString(volume));
}

class CityLabel extends SelectorLabel {
  final String? city;
  CityLabel(this.city)
      : super(city == null
            ? appLoc(MyApp.materialKey.currentContext!).allCities
            : localizedString(city));

  static List<DropdownMenuEntry<CityLabel>> entries(String country) {
    if (SettingsController.shared.destinationCities[country]?.firstOrNull ==
        null) {
      return [];
    }

    CityLabel all = CityLabel(null);

    return [
      DropdownMenuEntry<CityLabel>(value: all, label: all.label),
      ...SettingsController.shared.destinationCities[country]!.map((city) {
        CityLabel label = CityLabel(city);
        return DropdownMenuEntry<CityLabel>(value: label, label: label.label);
      })
    ];
  }
}

class PriceLabel extends SelectorLabel {
  final int? min;
  final int? max;
  PriceLabel(this.min, this.max)
      : super(localizedString(min == null
            ? appLoc(MyApp.materialKey.currentContext!).allPrices
            : (max == null
                ? '$min${appLoc(MyApp.materialKey.currentContext!).priceAbove}'
                : '$min - $max')));

  static List<DropdownMenuEntry<PriceLabel>> entries() {
    return priceFilters.map((item) {
      PriceLabel label = PriceLabel(item[0], item[1]);
      return DropdownMenuEntry<PriceLabel>(value: label, label: label.label);
    }).toList();
  }
}
