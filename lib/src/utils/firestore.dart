import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:driver/src/model/firestore.dart' as model;
import 'package:driver/src/model/user.dart';
import 'package:driver/src/utils/const.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

class Firestore {
  static Firestore shared = Firestore();

  Future<List<model.CityQuote>> getLatestQuotesForCities(
      {List<String> cities = const [],
      List<String> types = const [],
      int daysAgo = 7,
      bool distinctCity = true,
      bool allowSameQuoteParams = false,
      void Function(model.CityQuote)? progressCallback}) async {
    List<model.CityQuote> quotes = [];

    performQuery(Query query) async {
      final res = await query.get();
      for (final doc in res.docs) {
        final data = doc.data() as Map;
        if (!allowSameQuoteParams &&
            quotes
                .where((e) =>
                    e.destination == data['destination'] &&
                    e.typeName == data['typeName'])
                .isNotEmpty) {
          continue;
        }
        if (distinctCity &&
            quotes
                .where((e) => e.destination == data['destination'])
                .isNotEmpty) {
          continue;
        }
        try {
          model.CityQuote quote = model.CityQuote(
              destination: data['destination'],
              quotation: data['quotation'],
              type: data['type'],
              typeName: data['typeName'],
              created: (data['created'] as Timestamp).toDate(),
              updated: (data['updated'] as Timestamp).toDate(),
              delta: double.parse('${data['delta'] ?? 0}'));
          quotes.add(quote);
          if (progressCallback != null) {
            progressCallback(quote);
          }
        } catch (e) {
          debugPrint('Data Error: $data');
        }
      }
    }

    try {
      CollectionReference ref =
          FirebaseFirestore.instance.collection('quotationCollections');
      DateTime start = DateTime.now().subtract(Duration(days: daysAgo));
      Query query = ref
          .where('created', isGreaterThan: Timestamp.fromDate(start))
          .orderBy('created', descending: true);
      if (cities.isNotEmpty) {
        query = query.where('destination', whereIn: cities);
      }
      if (types.isNotEmpty) {
        query = query.where('typeName', whereIn: types);
      }
      await performQuery(query);
    } catch (e) {
      debugPrint('Error: $e');
    }

    return quotes;
  }

  Future<List<model.Order>> getOrders(bool isPublic,
      {List<String> statusList = const [
        OrderStatus.finding,
        OrderStatus.matching
      ],
      int limit = 10,
      String orderBy = 'updatedAt',
      bool descending = false,
      String? destinationCountry,
      String? destinationCity,
      int? minPrice,
      int? maxPrice,
      String? type,
      String? driverId}) async {
    if (!isPublic && !User.shared.canViewOrder()) {
      return [];
    }

    List<model.Order> orders = [];
    try {
      CollectionReference ref = FirebaseFirestore.instance.collection('orders');
      Query query = ref.where('status', whereIn: statusList);
      if (destinationCountry != null) {
        query =
            query.where('destinationCountry', isEqualTo: destinationCountry);
      }
      if (destinationCity != null) {
        query = query.where('destinationCity', isEqualTo: destinationCity);
      }
      if (minPrice != null) {
        query = query.where('targetPrice', isGreaterThanOrEqualTo: minPrice);
      }
      if (maxPrice != null) {
        query = query.where('targetPrice', isLessThanOrEqualTo: maxPrice);
      }
      if (type != null) {
        query = query.where('type', isEqualTo: type);
      }
      if (driverId != null) {
        query = query.where('driverId', isEqualTo: driverId);
      }
      final res = await query
          .orderBy(orderBy, descending: descending)
          .limit(limit)
          .get();
      for (var doc in res.docs) {
        final data = doc.data() as Map<String, dynamic>;
        orders.add(model.Order.fromMap(data));
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
    return orders;
  }

  Future<Map<String, dynamic>?> getDriverVerification() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        DocumentReference ref = FirebaseFirestore.instance
            .collection('driverVerification')
            .doc(uid);
        DocumentSnapshot doc = await ref.get();
        if (doc.exists) {
          return Map<String, dynamic>.from(doc.data() as Map);
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return null;
  }

  Future<model.Order?> getOrder(String id) async {
    try {
      DocumentReference ref =
          FirebaseFirestore.instance.collection('orders').doc(id);
      DocumentSnapshot doc = await ref.get();
      if (doc.exists) {
        return model.Order.fromMap(doc.data() as Map<String, dynamic>);
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
    return null;
  }

  Future<List<Map<String, dynamic>>> getTruckVerifications() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('truckVerification');
        Query query =
            ref.where('uid', isEqualTo: uid).where('deleted', isNull: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          return docs.docs.map((e) {
            Map<String, dynamic> data =
                Map<String, dynamic>.from(e.data() as Map);
            data['id'] = e.id;
            return data;
          }).toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }

  Future<Map<String, dynamic>?> createDriverVerification(
      {required String phone,
      required String name,
      required String passport,
      String? chinesePhone,
      String? wechat,
      model.Driver? driver}) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        DocumentReference ref = FirebaseFirestore.instance
            .collection('driverVerification')
            .doc(uid);
        DocumentSnapshot doc = await ref.get();
        if (doc.exists) {
          return Map<String, dynamic>.from(doc.data() as Map);
        }
        Map<String, dynamic> data = {
          'phone': phone,
          'name': name,
          'passport': passport,
          'status': model.VerificationStatus.pending.name,
          'chinesePhone': chinesePhone,
          'wechat': wechat,
          'driverId': driver?.id,
          'uid': uid,
          'created': Timestamp.now()
        };

        await ref.set(data);
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<Map<String, dynamic>?> createTruckVerification(
      {required String license,
      required String licenseStorage,
      required String type,
      required String tonnage,
      required String volume,
      model.Truck? truck}) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('truckVerification');

        Map<String, dynamic> data = {
          'uid': uid,
          'license': license,
          'licenseStorage': licenseStorage,
          'type': type,
          'tonnage': tonnage,
          'volume': volume,
          'truckId': truck?.id,
          'status': model.VerificationStatus.pending.name,
          'created': Timestamp.now(),
          'deleted': null
        };

        final added = await ref.add(data);
        data['id'] = added.id;
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<Map<String, dynamic>?> createBankInfo(model.BankInfo bankInfo) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('bankInfo');

        Map<String, dynamic> data = {
          'uid': uid,
          'type': bankInfo.type.name,
          'created': Timestamp.now(),
          'deleted': null
        };

        if (bankInfo.type == model.BankType.individual) {
          model.IndividualBankInfo info = bankInfo as model.IndividualBankInfo;
          data['name'] = info.name;
          data['account'] = info.account;
          data['bank'] = info.bank;
        } else {
          model.BusinessBankInfo info = bankInfo as model.BusinessBankInfo;
          data['name'] = info.name;
          data['bank'] = info.bank;
          data['account'] = info.account;
          data['swift'] = info.swift;
          data['address'] = info.address;
          data['taxNumber'] = info.taxNumber;
          data['beneficiaryCode'] = info.beneficiaryCode;
          data['usageCode'] = info.usageCode;
        }
        final added = await ref.add(data);
        data['id'] = added.id;
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<List<Map<String, dynamic>>> getBankInfos() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('bankInfo');
        Query query =
            ref.where('uid', isEqualTo: uid).where('deleted', isNull: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          return docs.docs.map((e) {
            Map<String, dynamic> data =
                Map<String, dynamic>.from(e.data() as Map);
            data['id'] = e.id;
            return data;
          }).toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }

  Future<model.OrderNegotiation?> getOrderNegotiation(model.Order order) async {
    if (User.shared.canViewOrder()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('orderNegotiations');
        Query query = ref
            .where('uid', isEqualTo: uid)
            .where('orderId', isEqualTo: order.orderId);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          return model.OrderNegotiation.fromMap(docs.docs.first.id,
              docs.docs.first.data() as Map<String, dynamic>);
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<List<model.Order>> getCancelledAndInvalidatedOrders() async {
    if (User.shared.canViewOrder()) {
      String uid = User.shared.user!.uid;
      try {
        List<model.Order> orders = [];
        CollectionReference orderRef =
            FirebaseFirestore.instance.collection('orders');

        Query cancelQuery = orderRef
            .where('driverId',
                isEqualTo: User.shared.driverVerification?.driverId)
            .where('status', isEqualTo: OrderStatus.cancelled);
        final res =
            await cancelQuery.orderBy('updatedAt', descending: true).get();
        for (var doc in res.docs) {
          final order = model.Order.fromMap(doc.data() as Map<String, dynamic>);
          orders.add(order);
        }

        CollectionReference ref =
            FirebaseFirestore.instance.collection('orderNegotiations');
        Query query = ref.where('uid', isEqualTo: uid);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          List<String> orderIds =
              docs.docs.map((e) => e.get('orderId') as String).toList();
          Query orderQuery = orderRef.where('orderId', whereIn: orderIds);
          final res =
              await orderQuery.orderBy('updatedAt', descending: true).get();
          for (var doc in res.docs) {
            final order = model.Order.fromMap(
                doc.data() as Map<String, dynamic>,
                isInvalid: true);
            if (order.status != OrderStatus.finding &&
                order.status != OrderStatus.matching &&
                order.driverId != User.shared.driverVerification?.driverId) {
              orders.add(order);
            }
          }
        }
        return orders;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }

  Future<List<model.Order>> getMatchingOrders() async {
    if (User.shared.canViewOrder()) {
      String uid = User.shared.user!.uid;
      try {
        List<model.Order> orders = [];
        CollectionReference orderRef =
            FirebaseFirestore.instance.collection('orders');

        Query query = orderRef
            .where('driverId',
                isEqualTo: User.shared.driverVerification?.driverId)
            .where('status', whereIn: [
          OrderStatus.pendingConfirm,
          OrderStatus.pendingContract
        ]);
        final res = await query.orderBy('updatedAt', descending: true).get();
        for (var doc in res.docs) {
          final order = model.Order.fromMap(doc.data() as Map<String, dynamic>);
          orders.add(order);
        }

        CollectionReference ref =
            FirebaseFirestore.instance.collection('orderNegotiations');
        query = ref.where('uid', isEqualTo: uid);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          List<String> orderIds =
              docs.docs.map((e) => e.get('orderId') as String).toList();
          Query orderQuery = orderRef.where('orderId', whereIn: orderIds).where(
              'status',
              whereIn: [OrderStatus.finding, OrderStatus.matching]);
          final res =
              await orderQuery.orderBy('updatedAt', descending: true).get();
          for (var doc in res.docs) {
            final data = doc.data() as Map<String, dynamic>;
            orders.add(model.Order.fromMap(data));
          }

          return orders;
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }

  Future<List<model.Notification>> getNotifications() async {
    List<model.Notification> notifications = [];
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('notifications');
        Query query = ref
            .where('uid', isEqualTo: uid)
            .orderBy('created', descending: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          notifications = docs.docs.map((e) {
            Map<String, dynamic> data =
                Map<String, dynamic>.from(e.data() as Map);
            data['id'] = e.id;
            return model.Notification.fromMap(data);
          }).toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return notifications;
  }

  Future<model.Notification?> markNotificationAsRead(
      model.Notification notification) async {
    if (User.shared.hasRegistered()) {
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('notifications');
        await ref.doc(notification.id).update({'unread': false});
        DocumentSnapshot doc = await ref.doc(notification.id).get();
        if (doc.exists) {
          Map<String, dynamic> data =
              Map<String, dynamic>.from(doc.data() as Map);
          data['id'] = doc.id;
          return model.Notification.fromMap(data);
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return null;
  }

  Future<bool> markAllNotificationAsRead() async {
    if (User.shared.hasRegistered()) {
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('notifications');
        final query = ref
            .where('uid', isEqualTo: User.shared.user!.uid)
            .where('unread', isEqualTo: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          for (var doc in docs.docs) {
            await doc.reference.update({'unread': false});
          }
          return true;
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return false;
  }

  Future<List<Map<String, dynamic>>> getOrderLogs(String orderId) async {
    List<Map<String, dynamic>> logs = [];
    if (User.shared.hasRegistered()) {
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('orderRecords');
        Query query = ref
            .where('orderId', isEqualTo: orderId)
            .orderBy('createdAt', descending: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          logs = docs.docs
              .map((e) {
                Map<String, dynamic> data =
                    Map<String, dynamic>.from(e.data() as Map);
                data['id'] = e.id;
                return data;
              })
              .where((e) => e['status'] != null)
              .toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return logs;
  }

  Future<String?> addDriverLocation(String orderId, Position position) async {
    if (User.shared.canViewOrder()) {
      try {
        final added =
            await FirebaseFirestore.instance.collection('driverLocation').add({
          'uid': User.shared.user!.uid,
          'orderId': orderId,
          'position': position.toJson(),
          'createdAt': Timestamp.now()
        });
        return added.id;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<Map<String, dynamic>?> getPayment(String id) async {
    try {
      DocumentSnapshot doc =
          await FirebaseFirestore.instance.collection('payments').doc(id).get();
      if (doc.exists) {
        Map<String, dynamic> data =
            Map<String, dynamic>.from(doc.data() as Map);
        data['id'] = doc.id;
        return data;
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
    return null;
  }

  Future<bool> createActivityReport(
      String orderId, String type, Position position, String? reason) async {
    if (User.shared.canViewOrder()) {
      try {
        String? positionId = await addDriverLocation(orderId, position);

        CollectionReference ref =
            FirebaseFirestore.instance.collection('activityReport');
        await ref.add({
          'uid': User.shared.user!.uid,
          'orderId': orderId,
          'type': type,
          'driverId': User.shared.driverVerification?.driverId,
          'location': positionId,
          'reason': reason,
          'createdAt': Timestamp.now()
        });
        return true;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return false;
  }

  Future<DateTime?> getLastPositionReportTime() async {
    try {
      CollectionReference ref =
          FirebaseFirestore.instance.collection('driverLocation');
      Query query = ref
          .where('uid', isEqualTo: User.shared.user!.uid)
          .orderBy('createdAt', descending: true)
          .limit(1);
      QuerySnapshot docs = await query.get();
      Map<String, dynamic>? map =
          docs.docs.firstOrNull?.data() as Map<String, dynamic>?;
      if (map != null) {
        return (map['createdAt'] as Timestamp?)?.toDate();
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
    return null;
  }
}
