import 'package:app_settings/app_settings.dart';
import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/order/detail.dart';
import 'package:driver/src/order/photo_upload.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

mixin OrderMixin<T extends StatefulWidget> on State<T> {
  Widget tag(String text, {Color? color}) {
    color ??= primaryColor.withAlpha(128);
    return Container(
        margin: EdgeInsets.only(right: 8),
        padding: EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
            border: Border.all(color: color),
            borderRadius: BorderRadius.circular(4)),
        child: Text(text, style: TextStyle(color: color)));
  }

  Widget title(String text) {
    return Text(text,
        style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 14,
            fontWeight: FontWeight.w500));
  }

  Widget subtitle(String text) {
    return Padding(
        padding: EdgeInsets.only(left: 14),
        child: Text(text,
            style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 12,
                fontWeight: FontWeight.w400)));
  }

  Widget orderCard(User user, Order order,
      {bool showViewOrderButton = true,
      bool showStatus = false,
      bool showPrice = true}) {
    final distance = Container(
        width: MediaQuery.of(context).size.width * 0.5,
        alignment: showStatus ? Alignment.centerLeft : Alignment.centerRight,
        child: Text(
            appLoc(context)
                .distance((order.distance / 1000).toStringAsFixed(1)),
            style: TextStyle(
                color: Color(0xFF999999),
                fontSize: 14,
                fontWeight: FontWeight.w400)));
    final status = tag(
        order.isInvalid && order.status != OrderStatus.cancelled
            ? appLoc(context).orderInvalidStatus
            : localizedString(order.status),
        color: statusColor());
    final price = Row(children: [
      Text('\$',
          style: TextStyle(
              color: errorColor, fontSize: 14, fontWeight: FontWeight.w400)),
      const SizedBox(width: 1),
      Text(maskPrice(user, order.actualCost ?? order.targetPrice),
          style: TextStyle(
              color: errorColor, fontSize: 20, fontWeight: FontWeight.w500))
    ]);
    return InkWell(
        onTap: canViewOrderDetail(order) && !order.isInvalid
            ? () {
                viewOrder(user, order);
              }
            : null,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
            width: MediaQuery.of(context).size.width - 32,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(8)),
            child: Column(children: [
              Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      border:
                          Border(bottom: BorderSide(color: Color(0xFFE1E3E6)))),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                            width: MediaQuery.of(context).size.width / 3,
                            child: title(
                                '${appLoc(context).pickupDate} ${order.expectedLoadingDate.toDateString()}')),
                        if (showStatus) status else distance
                      ])),
              Container(
                padding: EdgeInsets.all(12),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                  color: primaryColor,
                                  borderRadius: BorderRadius.circular(6)),
                            ),
                            const SizedBox(width: 8),
                            title(
                                '${appLoc(context).loading}: ${localizedString(order.origin)}')
                          ]),
                      subtitle(order.pickupAddress)
                    ]),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                  color: errorColor,
                                  borderRadius: BorderRadius.circular(6)),
                            ),
                            const SizedBox(width: 8),
                            title(
                                '${appLoc(context).unloading}: ${localizedString(order.destinationCountry)} - ${localizedString(order.destinationCity)}')
                          ]),
                      subtitle(order.destinationAddress)
                    ]),
              ),
              const SizedBox(height: 12),
              Padding(
                  padding: EdgeInsets.only(left: 12),
                  child: Row(children: [
                    tag(localizedString(order.type)),
                    if (order.tonnage.isNotEmpty)
                      tag(localizedString(order.tonnage.first)),
                    if (order.volumes.isNotEmpty)
                      tag(localizedString(order.volumes.first))
                  ])),
              Container(
                  margin: EdgeInsets.only(top: 12),
                  width: double.infinity,
                  height: 1,
                  color: Color(0xFFE1E3E6)),
              Container(
                  padding: EdgeInsets.all(12),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (showStatus) distance else price,
                        if (showViewOrderButton)
                          ElevatedButton(
                              onPressed: () => viewOrder(user, order),
                              style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  backgroundColor: primaryColor.withAlpha(20),
                                  foregroundColor: primaryColor,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                              child: Text(appLoc(context).viewOrder))
                        else if (showPrice)
                          price
                      ]))
            ])));
  }

  bool canViewOrderDetail(Order order) {
    return true;
  }

  Color? statusColor() {
    return null;
  }

  Future updateOrders({bool emptyOrders = true}) async {}

  Future viewOrder(User user, Order order) async {
    if (user.canViewOrder()) {
      await Navigator.of(context)
          .pushNamed(OrderDetailView.routeName, arguments: order.orderId);
      updateOrders(emptyOrders: false);
    } else {
      String message;
      String title;
      String primaryTitle;
      Function function;
      startLogin() => LoginMixin.startUserLogin(context);

      if (user.driverVerification == null) {
        title = appLoc(context).notVerifiedDriverTitle;
        message = appLoc(context).notVerifiedDriverMessage;
        primaryTitle = appLoc(context).goVerify;
        function = startLogin;
      } else if (user.driverVerification?.status ==
              VerificationStatus.pending ||
          user.driverVerification?.status == VerificationStatus.rejected) {
        title = appLoc(context).verifyingDriverTitle;
        message = appLoc(context).verifyingDriverMessage;
        primaryTitle = appLoc(context).customerService;
        function = customerService;
      } else if (user.truckVerifications
          .map((e) => e.status == VerificationStatus.pending)
          .isNotEmpty) {
        title = appLoc(context).verifyingTruckTitle;
        message = appLoc(context).verifyingTruckMessage;
        primaryTitle = appLoc(context).customerService;
        function = customerService;
      } else {
        title = appLoc(context).notVerifiedTruckTitle;
        message = appLoc(context).notVerifiedTruckMessage;
        primaryTitle = appLoc(context).goVerify;
        function = startLogin;
      }

      DialogButtonType? ret = await showAppDialog(
          context,
          dialogMessage(message: message),
          [
            dialogButton(
                context, appLoc(context).notYet, DialogButtonType.cancel),
            dialogButton(context, primaryTitle, DialogButtonType.primary)
          ],
          title: title);
      if (ret == DialogButtonType.primary && mounted) {
        function();
      }
    }
  }

  String orderStatusSubtitle(String status, {bool hasNegotiation = true}) {
    switch (status) {
      case OrderStatus.finding:
        return appLoc(context).statusFindingSubtitle;
      case OrderStatus.matching:
        if (hasNegotiation) {
          return appLoc(context).statusMatchingSubtitle;
        }
        return appLoc(context).statusPendingConfirmSubtitle;
      case OrderStatus.pendingConfirm:
        return appLoc(context).statusPendingConfirmSubtitle;
      case OrderStatus.pendingContract:
        return appLoc(context).statusPendingContractSubtitle;
      case OrderStatus.pendingLoad:
        return appLoc(context).statusPendingLoadSubtitle;
      case OrderStatus.queueing:
        return appLoc(context).statusQueueingSubtitle;
      case OrderStatus.inTransit:
        return appLoc(context).statusTransportSubtitle;
      case OrderStatus.inClearance:
        return appLoc(context).statusInClearanceSubtitle;
      case OrderStatus.pendingUnload:
        return appLoc(context).statusPendingUnloadSubtitle;
      case OrderStatus.completed:
        return appLoc(context).statusCompleteSubtitle;
      case OrderStatus.cancelled:
        return appLoc(context).statusCancelledSubtitle;
    }
    return '';
  }

  Future<Position?> getCurrentLocation() async {
    showLocationDialog() async {
      final ret = await showAppDialog(context,
          dialogMessage(message: appLoc(context).locationNotAvailableTitle), [
        dialogButton(context, appLoc(context).cancel, DialogButtonType.cancel),
        dialogButton(
            context, appLoc(context).openSettings, DialogButtonType.primary)
      ]);
      if (ret == DialogButtonType.primary) {
        AppSettings.openAppSettings(type: AppSettingsType.location);
      }
      return null;
    }

    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return await showLocationDialog();
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return await showLocationDialog();
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return await showLocationDialog();
    }

    if (mounted) {
      Loader.show(loadingText: appLoc(context).acquiringLocation);
    }
    Position? position;
    try {
      position = await Geolocator.getCurrentPosition(
          locationSettings: LocationSettings(timeLimit: Duration(seconds: 5)));
    } catch (e) {
      debugPrint('$e');
      if (mounted) {
        showSnackBar(context,
            type: SnackType.error, text: appLoc(context).acquireLocationFailed);
      }
    } finally {
      Loader.hide();
    }

    return position;
  }

  Future sendActivityReport(Order? order, String type,
      {String? reason, bool shouldPop = true}) async {
    if (order == null) {
      return;
    }
    final position = await getCurrentLocation();
    if (position == null) {
      return;
    }

    Loader.show();
    final ret = await fs.Firestore.shared
        .createActivityReport(order.orderId, type, position, reason);
    Loader.hide();
    if (mounted) {
      if (ret == true) {
        showSnackBar(context,
            type: SnackType.success, text: appLoc(context).operationSuccess);
        if (shouldPop) {
          Navigator.of(context).pop();
        }
      } else {
        showSnackBar(context,
            type: SnackType.error, text: appLoc(context).operationFailed);
      }
    }
  }

  Future<Order?> updateOrder(Order order, UpdateOrderFunction func,
      {Map<String, dynamic>? args}) async {
    final position = await getCurrentLocation();
    if (position == null) {
      return null;
    }

    Loader.show();
    final newOrder =
        await Functions.shared.updateOrder(order, position, func, args: args);
    Loader.hide();
    if (mounted) {
      if (newOrder != null) {
        showSnackBar(context,
            type: SnackType.success, text: appLoc(context).operationSuccess);
        return newOrder;
      } else {
        showSnackBar(context,
            type: SnackType.error, text: appLoc(context).operationFailed);
      }
    }

    return null;
  }

  Future<List<Order>> getOnGoingOrders() async {
    return await fs.Firestore.shared.getOrders(false,
        statusList: [
          OrderStatus.pendingLoad,
          OrderStatus.queueing,
          OrderStatus.inTransit,
          OrderStatus.inClearance,
          OrderStatus.pendingUnload
        ],
        driverId: User.shared.driverVerification?.driverId);
  }

  Future<Order?> doUploadPhoto(Order order, String title, String subtitle,
      UploadType type, UpdateOrderFunction func, String functionArgKey) async {
    return await Navigator.of(context).push<Order?>(MaterialPageRoute(
      builder: (context) => PhotoUploadView(
          title: title,
          order: order,
          subtitle: subtitle,
          type: type,
          function: func,
          functionArgKey: functionArgKey),
    ));
  }
}
