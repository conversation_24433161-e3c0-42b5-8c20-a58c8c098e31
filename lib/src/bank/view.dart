import 'package:driver/src/bank/info_entry.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:flutter/material.dart';

class BankManagementView extends StatefulWidget {
  const BankManagementView({super.key});

  static const String routeName = 'bank_management';

  @override
  State<StatefulWidget> createState() => _BankManagementViewState();
}

class _BankManagementViewState extends State<BankManagementView>
    with PageMixin {
  List<BankInfo> _bankInfoList = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Firestore.shared.getBankInfos().then((res) {
        if (res.isNotEmpty) {
          setState(() {
            _bankInfoList = res.map((e) => BankInfo.fromMap(e)).toList();
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(
        appLoc(context).bankInfo,
        _bankInfoList.isEmpty
            ? emptyIcon(type: EmptyIconType.bank, onTap: addBank)
            : ListView(
                shrinkWrap: true,
                children: [..._bankInfoList.map((e) => item(e))]),
        operation: BottomOperationPanel.singleOperation(
            context, appLoc(context).addBankTitle, addBank));
  }

  Future addBank() async {
    final res =
        await Navigator.of(context).pushNamed(BankInfoEntryView.routeName);
    if (res != null && res is Map<String, dynamic>) {
      setState(() {
        _bankInfoList.add(BankInfo.fromMap(res));
      });
    }
  }

  Widget item(BankInfo e) {
    Image bg;
    List<Text> texts = [];
    TextStyle style = TextStyle(color: Color(0xFF666666));
    if (e.type == BankType.individual) {
      bg = Image.asset('assets/images/bank_bg_i.png');
      texts.addAll([
        Text(
            '${appLoc(context).individualName}: ${(e as IndividualBankInfo).name}',
            style: style),
        Text('${appLoc(context).bankAccount}: ${e.account}', style: style),
      ]);
    } else {
      bg = Image.asset('assets/images/bank_bg_b.png');
      texts.addAll([
        Text(
            '${appLoc(context).businessOwnerName}: ${(e as BusinessBankInfo).name}',
            style: style),
        Text('${appLoc(context).taxNumber}: ${e.taxNumber}', style: style),
        Text('${appLoc(context).bankAccount}: ${e.account}', style: style),
      ]);
    }
    return dismissable(
        e.id!,
        Padding(
            padding: EdgeInsets.only(bottom: 10),
            child: Stack(children: [
              bg,
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 8,
                      children: [
                        Text(
                            e.type == BankType.individual
                                ? appLoc(context).individual
                                : appLoc(context).business,
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w500)),
                        ...texts
                      ]))
            ])),
        appLoc(context).deleteBankMessage, () async {
      bool ret = await Functions.shared.deleteBankInfo(e);
      if (ret) {
        setState(() {
          _bankInfoList.removeWhere((element) => element.id == e.id);
        });
      }
      return ret;
    });
  }
}
