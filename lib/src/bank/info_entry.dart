import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

class BankInfoEntryView extends StatefulWidget {
  const BankInfoEntryView({super.key});

  static const String routeName = 'bank_info_entry';

  @override
  State<StatefulWidget> createState() => _BankInfoEntryViewState();
}

class _BankInfoEntryViewState extends State<BankInfoEntryView>
    with UnfocusMixin, EntryMixin, PageMixin {
  BankType _type = BankType.individual;
  final TextEditingController _individualName = TextEditingController();
  final TextEditingController _individualBank = TextEditingController();
  final TextEditingController _individualAccount = TextEditingController();
  final TextEditingController _businessBank = TextEditingController();
  final TextEditingController _businessSwift = TextEditingController();
  final TextEditingController _businessAccount = TextEditingController();
  final TextEditingController _businessOwnerName = TextEditingController();
  final TextEditingController _businessTaxNumber = TextEditingController();
  final TextEditingController _businessAddress = TextEditingController();
  final TextEditingController _beneficiaryCode = TextEditingController();
  final TextEditingController _usageCode = TextEditingController();

  @override
  Widget build(BuildContext context) {
    List<Widget> form;
    if (_type == BankType.individual) {
      form = individualForm();
    } else {
      form = businessForm();
    }
    return unfocusContainer(pageBase(
        appLoc(context).addBankTitle,
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [typeSelect(), ...form]),
        operation: BottomOperationPanel.singleOperation(
            context, appLoc(context).submit, canSubmit() ? submit : null)));
  }

  Widget typeSelect() {
    return Padding(
        padding: EdgeInsets.only(bottom: 24),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(appLoc(context).selectBankType,
              style: TextStyle(color: Color(0xFF666666))),
          const SizedBox(height: 10),
          Row(children: [
            InkWell(
                splashFactory: NoSplash.splashFactory,
                highlightColor: Colors.transparent,
                onTap: () {
                  setState(() {
                    _type = BankType.individual;
                  });
                },
                child: Row(children: [
                  checkBox(_type == BankType.individual),
                  const SizedBox(width: 8),
                  Text(appLoc(context).individual,
                      style: TextStyle(fontSize: 16))
                ])),
            const SizedBox(width: 24),
            InkWell(
                splashFactory: NoSplash.splashFactory,
                highlightColor: Colors.transparent,
                onTap: () {
                  setState(() {
                    _type = BankType.business;
                  });
                },
                child: Row(children: [
                  checkBox(_type == BankType.business),
                  const SizedBox(width: 8),
                  Text(appLoc(context).business, style: TextStyle(fontSize: 16))
                ]))
          ])
        ]));
  }

  List<Widget> individualForm() {
    return [
      formItem(
          title: appLoc(context).individualName,
          isRequired: true,
          input: textInput(
              _individualName, appLoc(context).individualNamePlaceholder)),
      formItem(
          title: appLoc(context).bankName,
          isRequired: true,
          input:
              textInput(_individualBank, appLoc(context).bankNamePlaceholder)),
      formItem(
          title: appLoc(context).bankAccount,
          isRequired: true,
          input: textInput(
              _individualAccount, appLoc(context).bankAccountPlaceholder))
    ];
  }

  List<Widget> businessForm() {
    return [
      formItem(
          title: appLoc(context).bankName,
          isRequired: true,
          input: textInput(_businessBank, appLoc(context).bankNamePlaceholder)),
      formItem(
          title: appLoc(context).swiftAccount,
          isRequired: true,
          input: textInput(
              _businessSwift, appLoc(context).swiftAccountPlaceholder)),
      formItem(
          title: appLoc(context).bankAccount,
          isRequired: true,
          input: textInput(
              _businessAccount, appLoc(context).bankAccountPlaceholder)),
      formItem(
          title: appLoc(context).businessOwnerName,
          isRequired: true,
          input: textInput(_businessOwnerName,
              appLoc(context).businessOwnerNamePlaceholder)),
      formItem(
          title: appLoc(context).taxNumber,
          isRequired: true,
          input: textInput(
              _businessTaxNumber, appLoc(context).taxNumberPlaceholder)),
      formItem(
          title: appLoc(context).businessAddress,
          isRequired: true,
          input: textInput(
              _businessAddress, appLoc(context).businessAddressPlaceholder)),
      formItem(
          title: appLoc(context).beneficiaryCode,
          isRequired: true,
          input: textInput(
              _beneficiaryCode, appLoc(context).beneficiaryCodePlaceholder)),
      formItem(
          title: appLoc(context).usageCode,
          isRequired: true,
          input: textInput(_usageCode, appLoc(context).usageCodePlaceholder))
    ];
  }

  bool canSubmit() {
    if (_type == BankType.individual) {
      return _individualName.text.isNotEmpty &&
          _individualBank.text.isNotEmpty &&
          _individualAccount.text.isNotEmpty;
    } else {
      return _businessBank.text.isNotEmpty &&
          _businessSwift.text.isNotEmpty &&
          _businessAccount.text.isNotEmpty &&
          _businessOwnerName.text.isNotEmpty &&
          _businessTaxNumber.text.isNotEmpty &&
          _businessAddress.text.isNotEmpty &&
          _beneficiaryCode.text.isNotEmpty &&
          _usageCode.text.isNotEmpty;
    }
  }

  Future submit() async {
    Loader.show();
    final res = await fs.Firestore.shared.createBankInfo(
        _type == BankType.individual
            ? IndividualBankInfo(
                name: _individualName.text,
                account: _individualAccount.text,
                bank: _individualBank.text)
            : BusinessBankInfo(
                name: _businessOwnerName.text,
                account: _businessAccount.text,
                bank: _businessBank.text,
                swift: _businessSwift.text,
                taxNumber: _businessTaxNumber.text,
                address: _businessAddress.text,
                beneficiaryCode: _beneficiaryCode.text,
                usageCode: _usageCode.text));
    Loader.hide();
    if (res != null && mounted) {
      showSnackBar(context,
          type: SnackType.success, text: appLoc(context).submitSuccess);
      Navigator.of(context).pop(res);
    }
  }
}
