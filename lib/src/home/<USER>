import 'package:driver/src/home/<USER>';
import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/mixins/order.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/order/detail.dart';
import 'package:driver/src/order/photo_upload.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart' as fs;
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<StatefulWidget> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView>
    with TickerProviderStateMixin, PageMixin, EntryMixin, OrderMixin {
  OriginLabel? _selectedOrigin;
  DestLabel? _selectedDest;
  final TextEditingController _destController = TextEditingController();
  List<CityQuote> _cityQuotes = [];
  List<Order> _orders = [];
  late final TabController _tabController;
  int _tabIndex = 0;
  List<DropdownMenuEntry<CityLabel>> _cityEntries = [];
  CityLabel? _selectedCity;
  List<DropdownMenuEntry<PriceLabel>> _priceEntries = [];
  PriceLabel? _selectedPrice;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final controller = SettingsController.shared;
      if (controller.isFirstLaunch) {
        controller.updateFirstLaunch(false);
        LoginMixin.startUserLogin(context);
      } else {
        controller.addListener(updateSettings);
        updateOrders();
        User.shared.truckAddedListener = () {
          if (User.shared.defaultTruck() != null) {
            checkOnGoingOrder();
            updateOrders();
          }
        };
      }
    });
  }

  @override
  Future updateOrders({bool emptyOrders = true}) async {
    if (!mounted) return;
    setState(() {
      _orders = [];
    });
    String? destinationCountry;
    String? destinationCity;
    int? minPrice;
    int? maxPrice;
    String? type = User.shared.defaultTruck()?.type;
    if (_tabIndex == 1) {
      destinationCountry = _selectedDest?.country;
      destinationCity = _selectedCity?.city;
      minPrice = _selectedPrice?.min;
      maxPrice = _selectedPrice?.max;
    }
    final res = await fs.Firestore.shared.getOrders(true,
        destinationCountry: destinationCountry,
        destinationCity: destinationCity,
        minPrice: minPrice,
        maxPrice: maxPrice,
        type: type,
        orderBy: 'targetPrice',
        descending: true,
        limit: User.shared.canViewOrder() ? 30 : 10);
    setState(() {
      _orders = res;
    });
  }

  void updateSettings() {
    SettingsController controller = SettingsController.shared;
    List<String> quoteCities = controller.quoteCities;
    if (quoteCities.isNotEmpty) {
      fs.Firestore.shared
          .getLatestQuotesForCities(
              cities: quoteCities.sublist(0, controller.quoteCitiesInHomePage),
              types: controller.quoteTruckTypes.firstOrNull != null
                  ? [controller.quoteTruckTypes.first]
                  : [])
          .then((res) {
        setState(() {
          _cityQuotes = res;
        });
      });
      controller.removeListener(updateSettings);
    }

    if (controller.selectedDestCountry.isNotEmpty) {
      _selectedDest = destLabels(controller)
          .where((e) => e.country == controller.selectedDestCountry)
          .firstOrNull;
      if (_selectedDest != null) {
        _destController.text = _selectedDest!.label;
        Future.delayed(Duration(milliseconds: 200), () {
          _priceEntries = PriceLabel.entries();
          _selectedPrice = _priceEntries.first.value;
          updateCityEntries();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: backgroundColor,
        body: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
          Stack(children: [
            Image.asset('assets/images/banner.jpg',
                width: MediaQuery.of(context).size.width, fit: BoxFit.cover),
            Positioned(
                left: 24,
                bottom: 40,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(appLoc(context).use,
                          style: TextStyle(
                              fontSize: 28, fontWeight: FontWeight.w600)),
                      Text(appLoc(context).appTitle,
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w600))
                    ]))
          ]),
          locationPanel(),
          scrollPanel()
        ]));
  }

  Widget locationPanel() {
    return Container(
      width: MediaQuery.of(context).size.width - 32,
      transform: Matrix4.translationValues(0.0, -16.0, 0.0),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
                color: Color(0x1F000000),
                blurRadius: 16,
                spreadRadius: -6,
                offset: Offset(0, 6))
          ]),
      padding: EdgeInsets.all(10),
      child: settingProvider((controller) =>
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            locationSelector(originLabels(controller), (e) {
              if (e != null && e is OriginLabel) {
                setState(() {
                  _selectedOrigin = e;
                });
              }
            }, initialSelection: _selectedOrigin),
            locationSelector(destLabels(controller), (e) {
              setState(() {
                _selectedDest = e as DestLabel?;
                updateCityEntries();
                updateOrders();
              });
            },
                initialSelection: _selectedDest,
                hintText: appLoc(context).selectDestinationCountry,
                dotColor: errorColor,
                fillColor:
                    _selectedDest == null ? Color(0x1A029FB0) : Colors.white,
                controller: _destController,
                trailingIcon: _selectedDest != null
                    ? InkWell(
                        onTap: () {
                          setState(() {
                            _destController.text = '';
                            _selectedDest = null;
                            updateCityEntries();
                            updateOrders();
                          });
                        },
                        child: Icon(Icons.clear, size: 16))
                    : null),
            truckSelector()
          ])),
    );
  }

  Widget truckSelector() {
    return userProvider((user) {
      void Function() onTap;
      List<TextSpan> texts = [];
      TruckVerification? truck = user.defaultTruck();
      if (truck == null) {
        if (user.truckVerifications.isNotEmpty) {
          onTap = customerService;
          texts = [
            TextSpan(text: appLoc(context).loadingTruckNotVerified),
            TextSpan(
                text: appLoc(context).customerService,
                style: TextStyle(
                  decoration: TextDecoration.underline,
                )),
          ];
        } else {
          onTap = () => LoginMixin.startUserLogin(context);
          texts = [
            TextSpan(text: appLoc(context).loadingTruckNotRegistered),
            TextSpan(
                text: appLoc(context).registerTruck,
                style: TextStyle(
                  decoration: TextDecoration.underline,
                )),
          ];
        }
      } else {
        onTap = () async {
          if (user.verifiedTrucks.length > 1) {
            final selected = await showSelectionDialog(
                options: user.verifiedTrucks,
                title: appLoc(context).selectTruck,
                cancelText: appLoc(context).cancel,
                initialSelectIndex:
                    SettingsController.shared.defaultTruckIndex);
            if (selected != null) {
              user.updateDefaultTruck(selected);
              updateOrders();
            }
          }
        };
        texts = [
          TextSpan(
              text: '${appLoc(context).license}${truck.license} ${truck.type}'),
          if (user.verifiedTrucks.length > 1)
            TextSpan(
                text: appLoc(context).switchTruck,
                style: TextStyle(color: primaryColor)),
        ];
      }
      return Padding(
          padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
          child: InkWell(
              onTap: onTap,
              child: Text.rich(
                textAlign: TextAlign.start,
                TextSpan(
                  text: appLoc(context).loadingTruck,
                  style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
                  children: texts,
                ),
              )));
    });
  }

  Widget quotesPanel() {
    if (_cityQuotes.isEmpty) {
      return const SizedBox();
    }

    return userProvider((user) => InkWell(
        onTap: () {
          if (user.driverVerification != null) {
            Navigator.of(context).pushNamed(QuoteCenterView.routeName,
                arguments: _selectedOrigin?.city);
          }
        },
        child: Container(
          width: MediaQuery.of(context).size.width - 32,
          margin: EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Column(children: [
            Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFEEFDFF), Color(0xFFFFFFFF)])),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(appLoc(context).quoteCenter,
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF333333))),
                    Row(children: [
                      Text(
                          appLoc(context).quoteFrom(
                              localizedString(_selectedOrigin?.city ?? '')),
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF666666))),
                      Padding(
                          padding: EdgeInsets.only(top: 2),
                          child: Icon(Icons.arrow_forward_ios,
                              size: 12, color: Color(0xFF666666)))
                    ])
                  ],
                )),
            Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: _cityQuotes
                        .map((e) => Column(children: [
                              Row(children: [
                                Text(localizedString(e.destination),
                                    style: TextStyle(
                                        color: Color(0xFF333333),
                                        fontSize: 14)),
                                if (e.delta != 0)
                                  Padding(
                                      padding: EdgeInsets.only(left: 2),
                                      child: Image.asset(
                                          e.delta > 0
                                              ? 'assets/images/up.png'
                                              : 'assets/images/down.png',
                                          width: 10))
                              ]),
                              Row(children: [
                                Text('\$',
                                    style: TextStyle(
                                        color: errorColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400)),
                                const SizedBox(width: 1),
                                Text(maskPrice(user, e.quotation),
                                    style: TextStyle(
                                        color: errorColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w500)),
                                SizedBox(width: e.delta != 0 ? 12 : 0),
                              ])
                            ]))
                        .toList()))
          ]),
        )));
  }

  Widget prefFilters() {
    if (_selectedDest == null) {
      return SizedBox();
    }
    TextStyle style = TextStyle(fontSize: 12, color: Color(0xFF666666));
    double width = 160;
    return Row(children: [
      dropDown(_cityEntries, initialSelection: _selectedCity, onSelected: (e) {
        setState(() {
          _selectedCity = e;
          updateOrders();
        });
      }, width: width, textStyle: style),
      dropDown(_priceEntries, initialSelection: _selectedPrice,
          onSelected: (e) {
        setState(() {
          _selectedPrice = e;
          updateOrders();
        });
      }, width: width, textStyle: style)
    ]);
  }

  void updateCityEntries() {
    if (_selectedDest == null) {
      setState(() {
        _selectedCity = null;
        _cityEntries = [];
      });
    } else {
      setState(() {
        _cityEntries = CityLabel.entries(_selectedDest!.country);
        _selectedCity = _cityEntries.firstOrNull?.value;
      });
    }
    SettingsController.shared.updateSelectedDestCountry(_selectedDest?.country);
  }

  Widget orderList(User user) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          onTap: (i) {
            if (_tabIndex == i) {
              return;
            }
            setState(() {
              _tabIndex = i;
              updateOrders();
            });
          },
          isScrollable: true,
          indicatorColor: primaryColor,
          indicatorSize: TabBarIndicatorSize.label,
          dividerColor: Colors.transparent,
          labelColor: primaryColor,
          unselectedLabelColor: Color(0xFF666666),
          tabAlignment: TabAlignment.start,
          tabs: <Widget>[
            Tab(text: appLoc(context).rankByPrice),
            Tab(text: appLoc(context).rankByPref),
          ],
        ),
        (_tabIndex == 1 &&
                (_selectedDest == null || user.defaultTruck() == null))
            ? Container(
                width: MediaQuery.of(context).size.width - 64,
                padding: EdgeInsets.all(10),
                margin: EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                    color: Color(0x0DFF6802),
                    borderRadius: BorderRadius.circular(4)),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Color(0xFFFF6802), size: 20),
                    const SizedBox(width: 8),
                    Expanded(child: Text(appLoc(context).rankByPrefNotice))
                  ],
                ),
              )
            : Column(children: [
                _tabIndex == 1 ? prefFilters() : const SizedBox(height: 10),
                _orders.isEmpty
                    ? emptyIcon(type: EmptyIconType.order, paddingTop: 50)
                    : Column(
                        spacing: 10,
                        children:
                            _orders.map((e) => orderCard(user, e)).toList())
              ])
      ],
    );
  }

  Widget scrollPanel() {
    return Expanded(
        child: SizedBox(
      width: MediaQuery.of(context).size.width - 32,
      child: RefreshIndicator(
          backgroundColor: primaryColor,
          color: Colors.white,
          edgeOffset: 10,
          onRefresh: updateOrders,
          child: SingleChildScrollView(
              padding: EdgeInsets.only(bottom: 20),
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  quotesPanel(),
                  userProvider((user) => orderList(user))
                ],
              ))),
    ));
  }

  List<OriginLabel> originLabels(SettingsController controller) {
    List<OriginLabel> labels = controller.origins
        .map((e) => OriginLabel(e['country']!, e['city']!))
        .toList();
    _selectedOrigin = labels.firstOrNull;
    return labels;
  }

  List<DestLabel> destLabels(SettingsController controller) {
    List<DestLabel> labels =
        controller.destinationCountries.map((e) => DestLabel(e)).toList();
    return labels;
  }

  Widget locationSelector(List<SelectorLabel> labels,
      final ValueChanged<SelectorLabel?>? onSelected,
      {SelectorLabel? initialSelection,
      String? hintText,
      Color dotColor = primaryColor,
      Color fillColor = Colors.white,
      TextEditingController? controller,
      Widget? trailingIcon}) {
    List<DropdownMenuEntry<SelectorLabel>> entries = labels
        .map((label) => DropdownMenuEntry<SelectorLabel>(
            value: label,
            label: label.label,
            style: MenuItemButton.styleFrom(backgroundColor: Colors.white)))
        .toList();
    return dropDown(entries,
        leadingIcon: Icon(Icons.circle, color: dotColor, size: 16),
        width: MediaQuery.of(context).size.width - 52,
        hintText: hintText,
        controller: controller,
        trailingIcon: trailingIcon,
        initialSelection: initialSelection,
        onSelected: onSelected,
        textStyle: TextStyle(color: Color(0xFF333333), fontSize: 16),
        inputDecorationTheme: InputDecorationTheme(
            fillColor: fillColor,
            filled: true,
            outlineBorder: BorderSide.none,
            border: InputBorder.none,
            hintStyle: TextStyle(color: Color(0xFF666666))));
  }

  Future checkOnGoingOrder() async {
    Order? order = (await getOnGoingOrders()).firstOrNull;
    if (order != null && mounted) {
      String message = '';
      String buttonText = '';
      void Function() onTapPrimary = () => {};
      switch (order.status) {
        case OrderStatus.pendingLoad:
          if (order.weightPhoto == null) {
            message = appLoc(context).orderWeightPhotoMessage;
            onTapPrimary = () => doUploadPhoto(
                order,
                appLoc(context).weightPhoto,
                appLoc(context).weightPhotoPlaceholder,
                UploadType.weightPhoto,
                UpdateOrderFunction.startLoading,
                'weightPhoto');
            buttonText = appLoc(context).weightPhoto;
          } else {
            message = appLoc(context).orderFinishLoadMessage;
            onTapPrimary =
                () => updateOrder(order, UpdateOrderFunction.endLoading);
            buttonText = appLoc(context).finishLoad;
          }
          break;
        case OrderStatus.queueing:
        case OrderStatus.inTransit:
          DateTime? lastPositionReport =
              await fs.Firestore.shared.getLastPositionReportTime();
          if (lastPositionReport != null &&
              DateTime.now().difference(lastPositionReport).inHours < 2) {
            debugPrint('lastPositionReport $lastPositionReport');
            return;
          }
          if (!mounted) return;
          message = appLoc(context).orderPositionReportMessage;
          onTapPrimary = () => sendActivityReport(
              order, ActivityReportType.dailyReport,
              shouldPop: false);
          buttonText = appLoc(context).reportPosition;
        case OrderStatus.inClearance:
          message = appLoc(context).orderEndClearanceMessage;
          onTapPrimary =
              () => updateOrder(order, UpdateOrderFunction.endClearance);
          buttonText = appLoc(context).endClearance;
          break;
        case OrderStatus.pendingUnload:
          if (order.unloadingStart == null) {
            message = appLoc(context).orderArriveDestinationMessage;
            onTapPrimary =
                () => updateOrder(order, UpdateOrderFunction.startUnloading);
            buttonText = appLoc(context).arriveDestination;
          } else if (order.unloadingEnd == null) {
            message = appLoc(context).orderUploadReceiptMessage;
            onTapPrimary = () => doUploadPhoto(
                order,
                appLoc(context).uploadReceipt,
                appLoc(context).uploadReceiptPlaceholder,
                UploadType.receipt,
                UpdateOrderFunction.endUnloading,
                'receipt');
            buttonText = appLoc(context).unloadComplete;
          } else {
            return;
          }
          break;
        default:
          return;
      }
      if (!mounted) return;
      final res = await showAppDialog(
          context,
          dialogMessage(message: message),
          [
            dialogButton(
                context, appLoc(context).viewOrder, DialogButtonType.cancel),
            dialogButton(context, buttonText, DialogButtonType.primary)
          ],
          title: appLoc(context).onGoingOrder,
          showCancel: true);
      if (!mounted) return;
      if (res == DialogButtonType.primary) {
        onTapPrimary();
      } else if (res == DialogButtonType.cancel) {
        Navigator.of(context)
            .pushNamed(OrderDetailView.routeName, arguments: order.orderId);
      }
    }
  }
}
