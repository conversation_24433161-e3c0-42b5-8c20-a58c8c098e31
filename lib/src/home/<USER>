import 'package:driver/src/mixins/quote.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart' as ls;

class QuoteDetailView extends StatefulWidget {
  const QuoteDetailView({super.key});

  static const String routeName = 'quote_detail';

  @override
  State<StatefulWidget> createState() => _QuoteDetailViewState();
}

class _QuoteDetailViewState extends State<QuoteDetailView>
    with QuoteMixin, PageMixin {
  List<CityQuote> _cityQuotes = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final quote = ModalRoute.of(context)!.settings.arguments as CityQuote?;
      if (quote != null) {
        ls.Loader.show();
        final res = await Firestore.shared.getLatestQuotesForCities(
            cities: [quote.destination],
            daysAgo: 10,
            types: [quote.typeName],
            distinctCity: false,
            allowSameQuoteParams: true);
        setState(() {
          _cityQuotes = res;
        });
        ls.Loader.hide();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(
        appLoc(context).quoteCenter,
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: _cityQuotes.isEmpty
                ? emptyIcon()
                : SingleChildScrollView(
                    child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      quoteCard(_cityQuotes.first),
                      Padding(
                          padding: EdgeInsets.only(top: 16, bottom: 10),
                          child: Text(appLoc(context).quoteHistory,
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.w500))),
                      Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4)),
                          child: Column(children: [
                            row(
                                colHeader(appLoc(context).quoteTime),
                                colHeader(appLoc(context).quotePrice),
                                colHeader(appLoc(context).quoteDelta),
                                bottomPadding: 10),
                            ..._cityQuotes.map((e) => row(
                                Text(e.created.toMinuteString(),
                                    style: TextStyle(fontSize: 16)),
                                quotePrice(e),
                                quoteDelta(e)))
                          ]))
                    ],
                  ))),
        useScrollView: false);
  }

  Widget colHeader(String title) {
    return Text(title,
        style: TextStyle(fontSize: 14, color: Color(0xFF999999)));
  }

  Widget row(Widget firstCol, Widget secondCol, Widget thirdCol,
      {double bottomPadding = 20}) {
    return Padding(
        padding: EdgeInsets.only(bottom: bottomPadding),
        child: Row(children: [
          Expanded(child: firstCol),
          SizedBox(width: 80, child: secondCol),
          SizedBox(width: 80, child: thirdCol),
        ]));
  }
}
