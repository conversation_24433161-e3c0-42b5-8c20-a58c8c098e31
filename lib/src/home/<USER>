import 'package:driver/src/home/<USER>';
import 'package:driver/src/mixins/quote.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart' as ls;

class QuoteCenterView extends StatefulWidget {
  const QuoteCenterView({super.key});

  static const String routeName = 'quote_center';

  @override
  State<StatefulWidget> createState() => _QuoteCenterViewState();
}

class _QuoteCenterViewState extends State<QuoteCenterView>
    with QuoteMixin, PageMixin {
  List<DropdownMenuEntry<QuoteTruckTypeLabel>> _typeEntries = [];
  List<DropdownMenuEntry<QuoteAreaLabel>> _areaEntries = [];
  QuoteTruckTypeLabel? _type;
  QuoteAreaLabel? _area;
  List<CityQuote> _cityQuotes = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        List<String?> allTypes = [null];
        allTypes.addAll(SettingsController.shared.quoteTruckTypes);
        _typeEntries = allTypes.map((e) {
          QuoteTruckTypeLabel label = QuoteTruckTypeLabel(e);
          return DropdownMenuEntry<QuoteTruckTypeLabel>(
              value: label, label: label.label);
        }).toList();
        _type = _typeEntries.firstOrNull?.value;

        List<String?> allAreas = [null];
        allAreas.addAll(SettingsController.shared.quoteCities);
        _areaEntries = allAreas.map((e) {
          QuoteAreaLabel label = QuoteAreaLabel(e);
          return DropdownMenuEntry<QuoteAreaLabel>(
              value: label, label: label.label);
        }).toList();
        _area = _areaEntries.firstOrNull?.value;
        updateData();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(
        appLoc(context).quoteCenter,
        Column(
          children: [selection(), list()],
        ),
        useScrollView: false);
  }

  Future updateData() async {
    setState(() {
      _cityQuotes = [];
    });

    ls.Loader.show();
    final res = await Firestore.shared.getLatestQuotesForCities(
        cities: _area?.area == null ? [] : [_area!.area!],
        types: _type?.type == null ? [] : [_type!.type!],
        distinctCity: false);
    setState(() {
      _cityQuotes = res;
    });
    ls.Loader.hide();
  }

  Widget list() {
    return Expanded(
        child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: 40),
            child: _cityQuotes.isEmpty
                ? emptyIcon()
                : Column(
                    spacing: 10,
                    children: _cityQuotes
                        .map((e) => quoteCard(e,
                            onTap: () => Navigator.of(context).pushNamed(
                                QuoteDetailView.routeName,
                                arguments: e)))
                        .toList())));
  }

  Widget selection() {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 10),
        child: Row(children: [
          dropDown(_typeEntries,
              initialSelection: _typeEntries.firstOrNull?.value,
              onSelected: (value) {
            setState(() {
              _type = value;
              updateData();
            });
          }),
          dropDown(_areaEntries,
              initialSelection: _areaEntries.firstOrNull?.value,
              onSelected: (value) {
            setState(() {
              _area = value;
              updateData();
            });
          }, width: MediaQuery.of(context).size.width - 260)
        ]));
  }
}
