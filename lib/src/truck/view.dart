import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/truck/license_entry.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class TruckManagementView extends StatefulWidget {
  const TruckManagementView({super.key});

  static const String routeName = 'truck_management';

  @override
  State<StatefulWidget> createState() => _TruckManagementViewState();
}

class _TruckManagementViewState extends State<TruckManagementView>
    with PageMixin {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance
        .addPostFrameCallback((_) => User.shared.syncTruckVerifications());
  }

  @override
  Widget build(BuildContext context) {
    return userProvider((user) => pageBase(
        appLoc(context).truckManagement,
        user.truckVerifications.isEmpty
            ? emptyIcon(type: EmptyIconType.truck, onTap: addTruck)
            : ListView(shrinkWrap: true, children: [
                ...user.truckVerifications.map((e) => item(e, user))
              ]),
        operation: operationPanel(user)));
  }

  BottomOperationPanel operationPanel(User user) {
    bool hasPending = user.truckVerifications
        .where((e) => e.status == VerificationStatus.pending)
        .isNotEmpty;
    bool hasRejected = user.truckVerifications
        .where((e) => e.status == VerificationStatus.rejected)
        .isNotEmpty;
    if (hasPending || hasRejected) {
      return BottomOperationPanel.twoRowOperations(context,
          title1: appLoc(context).addTruckTitle,
          title2: hasRejected
              ? appLoc(context).contactUs
              : appLoc(context).contactForVerification,
          onPressed2: customerService);
    } else {
      return BottomOperationPanel.singleOperation(
          context, appLoc(context).addTruckTitle, addTruck);
    }
  }

  void addTruck() {
    Navigator.of(context).pushNamed(TruckLicenseEntryView.routeName);
  }

  Widget item(TruckVerification truck, User user) {
    return dismissable(
        truck.id,
        Container(
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width - 32,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
                padding: EdgeInsets.only(top: 4, right: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(truckImage(truck.type, ignoreSelection: true),
                        width: 100, fit: BoxFit.fitWidth),
                    const SizedBox(height: 2),
                    Text(truck.type,
                        style:
                            TextStyle(color: Color(0xFF999999), fontSize: 10))
                  ],
                )),
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: Text(
                            '${appLoc(context).license}: ${truck.license}',
                            style: TextStyle(
                                fontWeight: FontWeight.w500, fontSize: 16))),
                    status(truck)
                  ],
                ),
                const SizedBox(height: 6),
                Text('${truck.tonnage} | ${truck.volume}',
                    style: TextStyle(color: Color(0xFF666666)))
              ],
            ))
          ]),
        ),
        appLoc(context).deleteTruckMessage,
        () async => await user.removeTruckVerification(truck),
        canDismiss: () async {
      if (truck.status == VerificationStatus.pending) {
        await showAppDialog(
            context,
            dialogMessage(message: appLoc(context).preventTruckDelete),
            [
              dialogButton(
                  context, appLoc(context).understand, DialogButtonType.primary)
            ],
            title: appLoc(context).preventTruckDeleteTitle);
        return false;
      }
      return true;
    });
  }

  Widget status(TruckVerification truck) {
    String text;
    Color color;
    Icon icon;
    switch (truck.status) {
      case VerificationStatus.verified:
        text = appLoc(context).verified;
        color = Color(0xFF5ACF47);
        icon = Icon(Icons.check_circle_outline, color: color, size: 16);
        break;
      case VerificationStatus.pending:
        text = appLoc(context).verificationPending;
        color = Color(0xFF8A9899);
        icon = Icon(Icons.access_time, color: color, size: 16);
        break;
      case VerificationStatus.rejected:
        text = appLoc(context).verificationRejected;
        color = errorColor;
        icon = Icon(Icons.error_outline, color: color, size: 16);
        break;
    }

    return Container(
        padding: EdgeInsets.symmetric(vertical: 1, horizontal: 6),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: color, width: 1)),
        child: Row(children: [
          icon,
          const SizedBox(width: 2),
          Text(text, style: TextStyle(color: color))
        ]));
  }
}
