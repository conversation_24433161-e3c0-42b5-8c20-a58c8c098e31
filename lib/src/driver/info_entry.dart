import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/truck/view.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:driver/src/utils/firestore.dart' as fs;
import 'package:driver/src/widgets/dialog.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class DriverInfoEntryView extends StatefulWidget {
  const DriverInfoEntryView({super.key});

  static const String routeName = 'driver_info_entry';

  @override
  State<StatefulWidget> createState() => _DriverInfoEntryViewState();
}

class _DriverInfoEntryViewState extends State<DriverInfoEntryView>
    with UnfocusMixin, EntryMixin, PageMixin {
  final TextEditingController _phone = TextEditingController();
  final TextEditingController _name = TextEditingController();
  final TextEditingController _chinesePhone = TextEditingController();
  final TextEditingController _wechat = TextEditingController();
  final List<DropdownMenuEntry<CountryCode>> entries = CountryCode.entries;
  CountryCode? _selectedCountryCode;
  final TextEditingController _phoneSelect = TextEditingController();
  String? _passportStorage;

  @override
  void initState() {
    _selectedCountryCode = entries.first.value;
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _phoneSelect.text = _selectedCountryCode!.areaCode;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return unfocusContainer(pageBase(
        appLoc(context).userInfo,
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          phone(),
          name(),
          passport(),
          Text(appLoc(context).moreContactInfo,
              style: TextStyle(color: Color(0xFF666666))),
          const SizedBox(height: 10),
          chinesePhone(),
          wechat(),
        ]),
        operation: saveButton()));
  }

  Widget phone() {
    return formItem(
        title: appLoc(context).phone,
        isRequired: true,
        input: Row(children: [
          dropDown(
            entries,
            leadingIcon: Image.asset(
              'assets/images/country/${_selectedCountryCode!.code}.png',
              fit: BoxFit.fitWidth,
            ),
            controller: _phoneSelect,
            width: 124,
            inputDecorationTheme: InputDecorationTheme(
                prefixIconConstraints: BoxConstraints(maxWidth: 24),
                fillColor: Colors.white,
                filled: true,
                outlineBorder: BorderSide.none,
                border: InputBorder.none,
                hintStyle: TextStyle(color: Color(0xFF666666))),
            initialSelection: entries.first.value,
            onSelected: (e) {
              setState(() {
                _selectedCountryCode = e;
                _phoneSelect.text = e!.areaCode;
              });
            },
          ),
          Expanded(
              child: Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: textInput(_phone, appLoc(context).phonePlaceholder,
                      onBlur: findUserInfoByPhone,
                      keyboardType: TextInputType.number,
                      maxLength: _selectedCountryCode!.length)))
        ]));
  }

  Widget name() {
    return formItem(
        title: appLoc(context).driverName,
        isRequired: true,
        input: textInput(_name, appLoc(context).driverNamePlaceholder));
  }

  Widget passport() {
    return formItem(
        title: appLoc(context).passportPhoto,
        isRequired: true,
        input: imageUpload(_passportStorage, (value) {
          setState(() {
            _passportStorage = value;
          });
        }, 'passport', width: 200));
  }

  Widget chinesePhone() {
    return formItem(
        title: appLoc(context).chinesePhone,
        input: textInput(_chinesePhone, appLoc(context).chinesePhonePlaceholder,
            maxLength: 11));
  }

  Widget wechat() {
    return formItem(
        title: appLoc(context).wechat,
        input: textInput(_wechat, appLoc(context).wechatPlaceholder));
  }

  BottomOperationPanel saveButton() {
    saveInfo() async {
      Loader.show();
      final ret = await fs.Firestore.shared.createDriverVerification(
          phone: _selectedCountryCode!.areaCode.trim() + _phone.text,
          name: _name.text,
          passport: _passportStorage!,
          chinesePhone: _chinesePhone.text,
          wechat: _wechat.text,
          driver: _selectedDriver);
      Loader.hide();
      if (ret != null) {
        User.shared.updateDriverVerification(ret);
        if (mounted) {
          final continueVerification = await showAppDialog(
              context,
              dialogMessage(
                  message: appLoc(context).waitForDriverVerificationMessage),
              [
                dialogButton(
                    context, appLoc(context).notYet, DialogButtonType.cancel),
                dialogButton(
                    context, appLoc(context).goVerify, DialogButtonType.primary)
              ],
              title: appLoc(context).waitForDriverVerificationTitle,
              barrierDismissible: false);
          if (mounted) {
            if (continueVerification == DialogButtonType.primary) {
              Navigator.of(context)
                  .pushReplacementNamed(TruckManagementView.routeName);
            } else {
              Navigator.of(context).pop();
            }
          }
        }
      } else if (mounted) {
        showSnackBar(context,
            type: SnackType.error, text: appLoc(context).networkError);
      }
    }

    return BottomOperationPanel.singleOperation(
        context,
        appLoc(context).save,
        _name.text.isNotEmpty &&
                _phone.text.length == _selectedCountryCode!.length &&
                _passportStorage != null
            ? saveInfo
            : null);
  }

  Driver? _selectedDriver;

  Future findUserInfoByPhone() async {
    await findInfo(Functions.shared.findDriversWithPhoneNumber, _phone.text,
        (selected) async {
      _selectedDriver = selected;
      setState(() {
        _name.text = selected.value;
        _passportStorage = selected.passportImgStorage;
      });
      String? path = await getStorageImage('getPassportFromLark', selected);
      if (path != null) {
        setState(() {
          _selectedDriver?.passportImgStorage = path;
          _passportStorage = path;
        });
      }
    }, precondition: () => _phone.text.length == _selectedCountryCode!.length);
  }
}
