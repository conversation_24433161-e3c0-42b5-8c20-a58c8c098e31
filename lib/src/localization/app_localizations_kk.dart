import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Kazakh (`kk`).
class AppLocalizationsKk extends AppLocalizations {
  AppLocalizationsKk([String locale = 'kk']) : super(locale);

  @override
  String get appTitle => 'GoFreight';

  @override
  String get welcome => '欢迎来到';

  @override
  String get use => '欢迎使用';

  @override
  String get googleLogin => 'Google ұқық беріп тізімделу';

  @override
  String get appleLogin => 'Apple ұқық беріп тізімделу';

  @override
  String get agreementText => 'мен ';

  @override
  String get userAgreement => '«машина бағдарламалық құралдан пайдалану келісімін»、';

  @override
  String get privacyAgreement => '«машина бағдарламалық құралдың жеке құпиалығы келісімін»、';

  @override
  String get infoAgreement => '«машина информатсиаға уәде беру келісімін»';

  @override
  String get agreementText1 => ' оқып шықтым және оған қосыламын.';

  @override
  String get contactUs => 'мәселеге жолыққанда бізбен хабарласайық';

  @override
  String get notAgreeMessage1 => 'келісімді әлі егжей-тегжейлі оқымағаныңызды тексеріп көріңіз, ';

  @override
  String get notAgreeMessage2 => ' мұқиат оқып шығыңыз, біздің өнімдеріміз бен қізмет өтеуімізді «мақұлдауды» бастаңыз. ';

  @override
  String get cancel => 'күшінен қалдыру';

  @override
  String get agree => 'қосылу';

  @override
  String get readAgreement => 'келісімді оқыңыз';

  @override
  String get home => 'бас бет';

  @override
  String get order => 'Заказ';

  @override
  String get mine => 'өзім';

  @override
  String get networkError => '网络异常，请稍后再试';

  @override
  String loadFrom(Object origin) {
    return 'қытай $origin жүк тиеу';
  }

  @override
  String get selectDestinationCountry => 'бармақшы болған мемлекетті таңдаңыз';

  @override
  String get loadingTruck => 'тасымал автомобилы: ';

  @override
  String get loadingTruckNotRegistered => '您尚未维护车辆信息，去';

  @override
  String get registerTruck => '完善车辆信息';

  @override
  String get loadingTruckNotVerified => '车辆信息未认证，请';

  @override
  String get quoteCenter => 'базар жағдайы орталығы';

  @override
  String quoteFrom(Object origin) {
    return '$origin жолға шықты';
  }

  @override
  String get rankByPrice => 'жоғары бағада ретке тұрғызу';

  @override
  String get rankByPref => 'әуестену тәртібі';

  @override
  String get pickupDate => 'жүк тиеу уақыты:';

  @override
  String distance(Object distance) {
    return 'жалпы аралығы $distance km';
  }

  @override
  String get loading => 'жүк тиеу';

  @override
  String get unloading => 'жүк түсіру';

  @override
  String get noOrder => 'қәзірше зәкәз жоқ';

  @override
  String get noData => '暂无数据';

  @override
  String get viewOrder => 'тапсырысты тексеру';

  @override
  String get rankByPrefNotice => '您需要先在上方维护装车卸车地及运载车辆，才可以设置偏好';

  @override
  String get login => 'тіркелу';

  @override
  String get verifyTitle => 'шөфер текшені куәләндіріп тез арада Заказ тапсырып алды';

  @override
  String get verifyAction => 'куәләндіруғә бару';

  @override
  String get massiveOrders => 'қыруар зәкәз';

  @override
  String get fastPay => 'дер кезінде зәкәз беру';

  @override
  String get safe => 'текше кепілдігі';

  @override
  String get driverCenter => 'шөфер орталығы';

  @override
  String get driverVerify => 'шөферлік куәлігі';

  @override
  String get truckManagement => 'көлік басқару';

  @override
  String get bankInfo => 'бәнке информатсиасы';

  @override
  String get serviceCenter => 'қызмет ету орталығы';

  @override
  String get customerService => '联系客服';

  @override
  String get guarantee => '信息承诺协议';

  @override
  String get userAgreementAction => 'тұтынушылар келісімі';

  @override
  String get privacyAgreementAction => 'құпиа келісім';

  @override
  String get switchLanguage => 'тіл ауыстыру';

  @override
  String get completeInfo => 'тіркелу';

  @override
  String get userInfo => 'жеке информатсиа';

  @override
  String get phone => '电话';

  @override
  String get phonePlaceholder => '请输入您的电话';

  @override
  String get driverName => '司机姓名（英语）';

  @override
  String get driverNamePlaceholder => 'әті-жөніңізді енгізіңіз';

  @override
  String get passportPhoto => 'паспорттың суреті';

  @override
  String get moreContactInfo => '更多联系方式';

  @override
  String get chinesePhone => 'қытай телефон нөмірі';

  @override
  String get chinesePhonePlaceholder => 'қытай телефөн нөміріңізді кіргізіңіз.';

  @override
  String get wechat => 'WeChat нөмірі';

  @override
  String get wechatPlaceholder => 'WeChat нөміріңізді кіргізіңіз';

  @override
  String get save => 'сақтау';

  @override
  String get quickEntry => 'тез кіргізу';

  @override
  String get quickEntryMessage => '根据您填写的内容，匹配到如下信息，您可以选择正确信息快速填入';

  @override
  String get quickEntryNotAvailable => '请继续输入以下信息';

  @override
  String get confirmSelect => 'анықтап сұрыптау';

  @override
  String get cancelSelect => 'барлығы емес';

  @override
  String get imageFromAlbum => '从相册选择';

  @override
  String get imageFromCamera => '拍照';

  @override
  String get allQuoteTruckTypes => 'барлық машина түрі';

  @override
  String get allQuoteAreas => 'барлық жер';

  @override
  String get latestQuote => 'ең жаңа баға';

  @override
  String get quoteDelta => 'күндік құлдырау салыстырмасы';

  @override
  String get quoteDetail => 'базар жағдайы орталығы';

  @override
  String get quoteHistory => 'тарихи баға';

  @override
  String get quoteTime => 'бағаны мәлімдеу уақыты';

  @override
  String get quotePrice => 'баға';

  @override
  String get waitForDriverVerificationTitle => '提交审核';

  @override
  String get waitForDriverVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通。是否进行车辆认证？';

  @override
  String get waitForTruckVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通，审核通过后可立即接单。';

  @override
  String get notYet => 'қазыр толтырмаймыз';

  @override
  String get goVerify => 'дереу анықтаy';

  @override
  String get understand => '知道了';

  @override
  String get noTruck => 'уақытша машина жоқ，тыңнан ';

  @override
  String get add => 'көбейту';

  @override
  String get addTruckTitle => 'жаңадан қосылған автомобил';

  @override
  String get license => 'автомобил нөмірі';

  @override
  String get licensePlaceholder => '请输入车牌号';

  @override
  String get licenseImage => '车辆行驶证';

  @override
  String get next => 'келесі қадам';

  @override
  String get truckType => 'автомобил түрі';

  @override
  String get tonnage => 'әвтөмөбилге тиелген тонналық саны';

  @override
  String get tonnagePlaceholder => '请选择吨位';

  @override
  String get volume => 'әвтөмөбилге тиелген тонналық саны';

  @override
  String get volumePlaceholder => '请选择体积';

  @override
  String get confirm => 'тұрақтандыру';

  @override
  String get verified => '已认证';

  @override
  String get verificationEmpty => '待认证';

  @override
  String get verificationPending => '认证中';

  @override
  String get verificationRejected => '认证失败';

  @override
  String get verificationRejectedReason => '请联系客服';

  @override
  String get deleteTruckMessage => 'сіз қатысты көліктерді өшіріп жатырсыз，өшіруді тұрақтандыру?';

  @override
  String get confirmDelete => 'өшіруді тұрақтандыру';

  @override
  String get cancelDelete => 'өшірмеу';

  @override
  String get noBank => 'қәзірше бәнке информатсиасы жоқ, тыңнан ';

  @override
  String get addBank => 'көбейтіңіз';

  @override
  String get addBankTitle => 'жаңадан артқан бәнке информатсиасы';

  @override
  String get selectBankType => 'есеп түрін таңдау';

  @override
  String get individual => 'жеке адам';

  @override
  String get business => 'жеке өнеркәсіпші-саудагерлер';

  @override
  String get individualName => 'жеке адамның аты-жөні';

  @override
  String get individualNamePlaceholder => 'өзіңіздің әті-жөніңізді жазыңыз';

  @override
  String get bankName => 'есеп ашқан бәнкенің аты';

  @override
  String get bankNamePlaceholder => 'есеп ашқан бәнкенің атын енгізіңіз';

  @override
  String get bankAccount => 'есеп нөмірі';

  @override
  String get bankAccountPlaceholder => 'есеп нөміріңізді кіргізіңіз';

  @override
  String get submit => '提交';

  @override
  String get swiftAccount => 'KZT SWIFT балама нөмір BIC';

  @override
  String get swiftAccountPlaceholder => 'сіздің KZT SWIFT балама нөмір BIC';

  @override
  String get businessOwnerName => 'аты-жөні';

  @override
  String get businessOwnerNamePlaceholder => 'жеке өнеркәсіпші-саудагерлер аты-жөнін енгізіңіз';

  @override
  String get taxNumber => 'бажы нөмірі';

  @override
  String get taxNumberPlaceholder => 'бажы нөміріңізді кіргізіңіз';

  @override
  String get businessAddress => 'тіркелген әдрес';

  @override
  String get businessAddressPlaceholder => 'жеке өнеркәсіпші-саудагерлер тіркелген әдрес';

  @override
  String get beneficiaryCode => 'игіліктенушінің шартты нөмірі';

  @override
  String get beneficiaryCodePlaceholder => 'игіліктенушінің шартты нөмірін кіргізіңіз';

  @override
  String get usageCode => 'ақша төлеудің қолданылу шарт таңбасы';

  @override
  String get usageCodePlaceholder => 'ақша төлеудің істетілу орнын енгізіңіз';

  @override
  String get deleteBankMessage => 'сіз қатысты бәнке информатсиасын өшіріп жатырсыз, өшіруді тұрақтандырдыңыз ба?';

  @override
  String get submitSuccess => 'тапсырылып болу';

  @override
  String get switchTruck => ' ауыстыру';

  @override
  String get loadingImage => '正在下载图片';

  @override
  String get selectTruck => '选择车辆';

  @override
  String get uid => '用户编号';

  @override
  String get copyToClipboard => '已复制到剪贴板';

  @override
  String get preventTruckDeleteTitle => '车辆信息认证中';

  @override
  String get preventTruckDelete => '车辆信息正在认证，请稍后再操作，或者联系客服加速处理。';

  @override
  String get contactForVerification => '联系客服加速认证';

  @override
  String get notVerifiedDriverTitle => '尚未认证';

  @override
  String get notVerifiedDriverMessage => '您的资料不完善，目前不能接单，请及时去完善您的资料，立即开启接单';

  @override
  String get notVerifiedTruckTitle => '暂无车辆';

  @override
  String get notVerifiedTruckMessage => '您尚未维护车辆信息，目前不能接单，请及时去完善车辆信息，立即开启接单';

  @override
  String get verifyingDriverTitle => '正在认证';

  @override
  String get verifyingDriverMessage => '您的资料正在认证中，目前不能接单，您可联系客服加速认证';

  @override
  String get verifyingTruckTitle => '车辆认证中';

  @override
  String get verifyingTruckMessage => '您维护车辆信息正在认证中，目前不能接单，您可联系客服加速认证';

  @override
  String get orderDetail => 'тапсырыс егжей-тегжейлі';

  @override
  String get orderDistance => 'тапсырыстың жалпы аралығы';

  @override
  String get orderLoadingAddress => 'жүк тиеу орны';

  @override
  String get orderUnloadingAddress => 'жүк түсірілетін жер';

  @override
  String get orderLoadingTime => '装货日期';

  @override
  String get orderCost => 'тасымал ақысы';

  @override
  String get makeQuote => 'бағаны мәлімдеу жөнінде кеңесу';

  @override
  String get placeOrder => 'дереу Заказ қабылдау';

  @override
  String get orderManagement => 'тапсырыс бойынша басқару';

  @override
  String get orderStatusMatching => '匹配中';

  @override
  String get orderStatusOngoing => 'жүргізіліп жатыр';

  @override
  String get orderStatusFinished => 'айақталды';

  @override
  String get orderStatusCanceled => 'күшінен қалдырылды';

  @override
  String get allCities => 'барлық жер';

  @override
  String get allPrices => 'толық баға';

  @override
  String get priceAbove => ' нан жоғары';

  @override
  String get customerServiceWithWhatsapp => '即将通过 WhatsApp 联系客服';

  @override
  String get customerServiceWithPhone => '即将通过电话联系客服';

  @override
  String get contact => '联系';

  @override
  String get orderTargetPrice => '货主目前报价';

  @override
  String get confirmQuote => '确认出价';

  @override
  String get cancelQuote => '取消出价';

  @override
  String get selectYourTruck => '请选择您的车辆';

  @override
  String get enterYourQuote => '请输入您的报价';

  @override
  String get operationSuccess => '操作成功';

  @override
  String get operationFailed => '操作失败，请稍后重试';

  @override
  String get quoteRequired => '请输入您的报价';

  @override
  String get confirmPlaceOrder => '确认接单';

  @override
  String get cancelPlaceOrder => '取消接单';

  @override
  String get placingOrder => '正在处理订单，请稍后';

  @override
  String get notifications => '消息通知';

  @override
  String get statusFindingSubtitle => '货主发布新的订单';

  @override
  String get statusMatchingSubtitle => '系统正在筛选匹配中';

  @override
  String get statusPendingConfirmSubtitle => '已经意向接单，等待货主确认';

  @override
  String get statusPendingContractSubtitle => '合同正在签署中，随时注意电话';

  @override
  String get statusPendingLoadSubtitle => '接单成功，尽快前往装货地装货吧';

  @override
  String get statusQueueingSubtitle => '排队中，耐心等待';

  @override
  String get statusTransportSubtitle => '开始运输了，记得每天报备位置哦';

  @override
  String get statusInClearanceSubtitle => '排队清关中，耐心等待';

  @override
  String get statusHeadingDestinationSubtitle => '正在前往目的地，运输中有问题请事件报备';

  @override
  String get statusPendingUnloadSubtitle => '已经到达目的地，请及时卸货哦';

  @override
  String get statusCompleteSubtitle => '订单已完成，感谢您一路护送';

  @override
  String get statusCancelledSubtitle => '订单已取消，如有问题请联系沟通哦';

  @override
  String get contactService => '沟通联系';

  @override
  String get orderInvalidStatus => '已失效';

  @override
  String get logs => '日志';

  @override
  String get weightPhoto => 'таразылауды фотаға түсіру';

  @override
  String get weightPhotoPlaceholder => '请上传车辆过磅照片';

  @override
  String get finishLoad => '装货完成';

  @override
  String get activityReport => 'оқиғаны тізімге алдыру';

  @override
  String get locationNotAvailableTitle => '定位失败';

  @override
  String get locationNotAvailableMessage => '需要获取您的定位处理运输订单，请检查您的定位权限是否开启。';

  @override
  String get openSettings => '打开设置';

  @override
  String get acquiringLocation => '正在获取定位';

  @override
  String get viewPayment => '查看凭证';

  @override
  String get temporaryStay => '临时休息';

  @override
  String get temporaryStayPlaceholder => '请输入休息原因';

  @override
  String get unexpectedDelay => 'ерекше кешеуілдету';

  @override
  String get unexpectedDelayPlaceholder => '请输入延误原因';

  @override
  String get selectReportItem => 'мәлімдейтін істерді таңдау';

  @override
  String get acquireLocationFailed => '定位失败，请稍后再试';

  @override
  String get dailyReport => 'әр күні тізімге алдыру';

  @override
  String get startClearance => '开始清关';

  @override
  String get endClearance => '清关完毕';

  @override
  String get arriveDestination => '到达目的地';

  @override
  String get unloadComplete => '卸货完毕';

  @override
  String get uploadReceipt => '上传交接单';

  @override
  String get uploadReceiptPlaceholder => '请上传交接单照片';

  @override
  String get cannotTakeOrderTitle => '无法接单';

  @override
  String get cannotTakeOrderMessage => '您目前有正在进行中的订单，请完成当前订单后再接新订单。';

  @override
  String get signOut => '退出登录';

  @override
  String get signOutMessage => '您正在退出账户，请确认是否退出。';

  @override
  String get deleteAccount => '注销账号';

  @override
  String get deleteAccountMessage => '您正在注销账户，注销后数据将清除，请确认。';

  @override
  String get accountDeletedMessage => '账号已注销，请联系客服。';

  @override
  String get unauthenticatedError => 'App认证失败，请从官方渠道下载App';

  @override
  String get onGoingOrder => '进行中订单';

  @override
  String get orderWeightPhotoMessage => '请您尽快前往装货地装货，记得一定要过磅拍照哈。';

  @override
  String get orderFinishLoadMessage => '您是否已经装车完毕，如果已经装货完毕，请点击装货完毕。';

  @override
  String get orderStartClearanceMessage => '您可能已经到了海关清关，请确认是否已经开始清关。';

  @override
  String get orderEndClearanceMessage => '您可能已经完成海关清关，请确认是否已完成清关。';

  @override
  String get orderArriveDestinationMessage => '您可能已经到达卸货地，请确认是否已到达目的地。';

  @override
  String get orderUploadReceiptMessage => '您可能已经卸货完成，请上传交接单。';

  @override
  String get orderPositionReportMessage => '您有进行中订单，请点击报备位置。';

  @override
  String get reportPosition => '报备位置';

  @override
  String get guestAccess => 'Guest Access';
}
