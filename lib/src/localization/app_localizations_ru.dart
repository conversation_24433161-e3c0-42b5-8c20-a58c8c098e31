import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'GoFreight';

  @override
  String get welcome => 'Добро пожаловать в';

  @override
  String get use => 'Добро пожаловать';

  @override
  String get googleLogin => 'Вход с помощью Google';

  @override
  String get appleLogin => 'Авторизация Apple';

  @override
  String get agreementText => 'Я прочитал и согласен с ';

  @override
  String get userAgreement => '《Соглашением об использовании программного обеспечения GoFreight》、';

  @override
  String get privacyAgreement => '《Соглашением о конфиденциальности GoFreight》、';

  @override
  String get infoAgreement => '《Соглашением о предоставлении информации GoFreight》';

  @override
  String get agreementText1 => '.';

  @override
  String get contactUs => 'Если возникли проблемы, свяжитесь с нами';

  @override
  String get notAgreeMessage1 => 'Обнаружено, что вы еще не прочитали соглашение. Пожалуйста, внимательно прочитайте и примите';

  @override
  String get notAgreeMessage2 => ', Нажмите «Согласен», чтобы начать пользоваться нашими продуктами и услугами.';

  @override
  String get cancel => 'Отменить';

  @override
  String get agree => 'Согласен';

  @override
  String get readAgreement => 'Прочитайте соглашение';

  @override
  String get home => 'Главная';

  @override
  String get order => 'Заказ';

  @override
  String get mine => 'Мой';

  @override
  String get networkError => '网络异常，请稍后再试';

  @override
  String loadFrom(Object origin) {
    return 'Из $origin';
  }

  @override
  String get selectDestinationCountry => 'Выберите страну, в которую вы собираетесь отправиться';

  @override
  String get loadingTruck => 'Транспортное средство: ';

  @override
  String get loadingTruckNotRegistered => '您尚未维护车辆信息，去';

  @override
  String get registerTruck => '完善车辆信息';

  @override
  String get loadingTruckNotVerified => '车辆信息未认证，请';

  @override
  String get quoteCenter => 'Центр цен';

  @override
  String quoteFrom(Object origin) {
    return 'Отправление из $origin';
  }

  @override
  String get rankByPrice => 'Сортировка по цене';

  @override
  String get rankByPref => 'Сортировка по предпочтениям';

  @override
  String get pickupDate => 'Время получения:';

  @override
  String distance(Object distance) {
    return 'Общая протяженность $distance km';
  }

  @override
  String get loading => 'Погрузка';

  @override
  String get unloading => 'Разгрузка';

  @override
  String get noOrder => 'Нет заказов';

  @override
  String get noData => '暂无数据';

  @override
  String get viewOrder => 'Просмотреть заказ';

  @override
  String get rankByPrefNotice => 'Вам необходимо сейчас вверху указать место погрузки и разгрузки, а также транспортное средство，только после этого вы сможете установить предпочтения';

  @override
  String get login => 'Вход';

  @override
  String get verifyTitle => 'Сертифицированный водитель платформы, быстрое принятие заказа';

  @override
  String get verifyAction => 'Перейти к сертификации';

  @override
  String get massiveOrders => 'Большое количество заказов';

  @override
  String get fastPay => 'Своевременные расчеты';

  @override
  String get safe => 'Гарантии платформы';

  @override
  String get driverCenter => 'Центр водителей';

  @override
  String get driverVerify => 'Сертификация водителей';

  @override
  String get truckManagement => 'Управление транспортными средствами';

  @override
  String get bankInfo => 'Банковская информация';

  @override
  String get serviceCenter => 'Сервисный центр';

  @override
  String get customerService => '联系客服';

  @override
  String get guarantee => '信息承诺协议';

  @override
  String get userAgreementAction => 'Пользовательское соглашение';

  @override
  String get privacyAgreementAction => 'Политика конфиденциальности';

  @override
  String get switchLanguage => 'Смена языка';

  @override
  String get completeInfo => 'Пожалуйста, дополните информацию';

  @override
  String get userInfo => 'Личная информация';

  @override
  String get phone => '电话';

  @override
  String get phonePlaceholder => '请输入您的电话';

  @override
  String get driverName => '司机姓名（英语）';

  @override
  String get driverNamePlaceholder => 'Введите свое имя';

  @override
  String get passportPhoto => 'Фотография';

  @override
  String get moreContactInfo => '更多联系方式';

  @override
  String get chinesePhone => 'Телефон в Китае';

  @override
  String get chinesePhonePlaceholder => 'Введите свой телефон в Китае';

  @override
  String get wechat => 'WeChat';

  @override
  String get wechatPlaceholder => 'Введите свой WeChat ID';

  @override
  String get save => 'Сохранить';

  @override
  String get quickEntry => 'Быстрый ввод';

  @override
  String get quickEntryMessage => '根据您填写的内容，匹配到如下信息，您可以选择正确信息快速填入';

  @override
  String get quickEntryNotAvailable => '请继续输入以下信息';

  @override
  String get confirmSelect => 'Подтвердить выбор';

  @override
  String get cancelSelect => 'Ни одно из вышеперечисленных';

  @override
  String get imageFromAlbum => '从相册选择';

  @override
  String get imageFromCamera => '拍照';

  @override
  String get allQuoteTruckTypes => 'Все модели';

  @override
  String get allQuoteAreas => 'Все регионы';

  @override
  String get latestQuote => 'Последние предложения';

  @override
  String get quoteDelta => 'Ежедневный рост/падение';

  @override
  String get quoteDetail => 'Детальная информация';

  @override
  String get quoteHistory => 'Исторические котировки';

  @override
  String get quoteTime => 'Время котировки';

  @override
  String get quotePrice => 'Цена';

  @override
  String get waitForDriverVerificationTitle => '提交审核';

  @override
  String get waitForDriverVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通。是否进行车辆认证？';

  @override
  String get waitForTruckVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通，审核通过后可立即接单。';

  @override
  String get notYet => 'Сейчас не могу';

  @override
  String get goVerify => 'Пройти проверку';

  @override
  String get understand => '知道了';

  @override
  String get noTruck => 'Нет транспортных средств, добавить ';

  @override
  String get add => 'новый';

  @override
  String get addTruckTitle => 'новый добавленный автомобиль';

  @override
  String get license => 'номерной знак';

  @override
  String get licensePlaceholder => '请输入车牌号';

  @override
  String get licenseImage => 'Технический паспорт';

  @override
  String get next => 'Следующий';

  @override
  String get truckType => 'Тип автомобиля';

  @override
  String get tonnage => 'Грузоподъемность';

  @override
  String get tonnagePlaceholder => '请选择吨位';

  @override
  String get volume => 'Объем кузова';

  @override
  String get volumePlaceholder => '请选择体积';

  @override
  String get confirm => 'Подтверждено';

  @override
  String get verified => '已认证';

  @override
  String get verificationEmpty => 'Ожидающие сертификации';

  @override
  String get verificationPending => '认证中';

  @override
  String get verificationRejected => '认证失败';

  @override
  String get verificationRejectedReason => '请联系客服';

  @override
  String get deleteTruckMessage => 'Вы удаляете связанные транспортные средства. Подтвердить удаление?';

  @override
  String get confirmDelete => 'Подтвердить удаление';

  @override
  String get cancelDelete => 'Не удалять';

  @override
  String get noBank => 'Информация о банке отсутствует, ';

  @override
  String get addBank => 'добавить';

  @override
  String get addBankTitle => 'Добавлена информация о банке';

  @override
  String get selectBankType => 'Выберите тип счета';

  @override
  String get individual => 'Физическое лицо';

  @override
  String get business => 'Индивидуальный предприниматель';

  @override
  String get individualName => 'Имя и фамилия';

  @override
  String get individualNamePlaceholder => 'Введите свое имя';

  @override
  String get bankName => 'Название банка, в котором открыт счет';

  @override
  String get bankNamePlaceholder => 'Введите название банка, в котором открыт счет';

  @override
  String get bankAccount => 'Номер счета';

  @override
  String get bankAccountPlaceholder => 'Введите номер своего счета';

  @override
  String get submit => '提交';

  @override
  String get swiftAccount => 'KZT Код SWIFT';

  @override
  String get swiftAccountPlaceholder => 'Введите код SWIFT вашего банка';

  @override
  String get businessOwnerName => 'Имя';

  @override
  String get businessOwnerNamePlaceholder => 'Введите название вашего индивидуального предпринимателя';

  @override
  String get taxNumber => 'Налоговый номер';

  @override
  String get taxNumberPlaceholder => 'Введите ваш налоговый номер';

  @override
  String get businessAddress => 'Адрес регистрации';

  @override
  String get businessAddressPlaceholder => 'Введите адрес регистрации вашего индивидуального предпринимателя';

  @override
  String get beneficiaryCode => 'Код бенефициара';

  @override
  String get beneficiaryCodePlaceholder => 'Введите код бенефициара';

  @override
  String get usageCode => 'Код назначения платежа*';

  @override
  String get usageCodePlaceholder => 'Введите код цели платежа';

  @override
  String get deleteBankMessage => '您正在删除银行信息，确认删除吗？';

  @override
  String get submitSuccess => 'Отправлено';

  @override
  String get switchTruck => ' с переключением';

  @override
  String get loadingImage => '正在下载图片';

  @override
  String get selectTruck => '选择车辆';

  @override
  String get uid => '用户编号';

  @override
  String get copyToClipboard => '已复制到剪贴板';

  @override
  String get preventTruckDeleteTitle => '车辆信息认证中';

  @override
  String get preventTruckDelete => '车辆信息正在认证，请稍后再操作，或者联系客服加速处理。';

  @override
  String get contactForVerification => '联系客服加速认证';

  @override
  String get notVerifiedDriverTitle => 'Не подтверждено';

  @override
  String get notVerifiedDriverMessage => 'Ваши данные неполны, в настоящее время мы не можем принимать заказы. Пожалуйста дополните свои данные, чтобы сразу начать принимать заказы';

  @override
  String get notVerifiedTruckTitle => 'В настоящее время нет транспортных средств';

  @override
  String get notVerifiedTruckMessage => 'Вы еще не обновили информацию о транспортном средстве, поэтому в настоящее время не можете принимать заказы. Пожалуйста, дополните информацию о транспортном средстве и сразу же начните принимать заказы';

  @override
  String get verifyingDriverTitle => '正在认证';

  @override
  String get verifyingDriverMessage => '您的资料正在认证中，目前不能接单，您可联系客服加速认证';

  @override
  String get verifyingTruckTitle => '车辆认证中';

  @override
  String get verifyingTruckMessage => '您维护车辆信息正在认证中，目前不能接单，您可联系客服加速认证';

  @override
  String get orderDetail => '订单详情';

  @override
  String get orderDistance => '订单全程距离';

  @override
  String get orderLoadingAddress => '装货地点';

  @override
  String get orderUnloadingAddress => '卸货地点';

  @override
  String get orderLoadingTime => '装货日期';

  @override
  String get orderCost => '运费';

  @override
  String get makeQuote => '报价协商';

  @override
  String get placeOrder => '立即接单';

  @override
  String get orderManagement => 'Управление заказами';

  @override
  String get orderStatusMatching => '匹配中';

  @override
  String get orderStatusOngoing => 'В процессе';

  @override
  String get orderStatusFinished => 'Завершено';

  @override
  String get orderStatusCanceled => 'Отменено';

  @override
  String get allCities => 'Все регионы';

  @override
  String get allPrices => 'Все цены';

  @override
  String get priceAbove => ' Более';

  @override
  String get customerServiceWithWhatsapp => '即将通过 WhatsApp 联系客服';

  @override
  String get customerServiceWithPhone => '即将通过电话联系客服';

  @override
  String get contact => '联系';

  @override
  String get orderTargetPrice => '货主目前报价';

  @override
  String get confirmQuote => '确认出价';

  @override
  String get cancelQuote => '取消出价';

  @override
  String get selectYourTruck => '请选择您的车辆';

  @override
  String get enterYourQuote => '请输入您的报价';

  @override
  String get operationSuccess => '操作成功';

  @override
  String get operationFailed => '操作失败，请稍后重试';

  @override
  String get quoteRequired => '请输入您的报价';

  @override
  String get confirmPlaceOrder => '确认接单';

  @override
  String get cancelPlaceOrder => '取消接单';

  @override
  String get placingOrder => '正在处理订单，请稍后';

  @override
  String get notifications => '消息通知';

  @override
  String get statusFindingSubtitle => '货主发布新的订单';

  @override
  String get statusMatchingSubtitle => '系统正在筛选匹配中';

  @override
  String get statusPendingConfirmSubtitle => '已经意向接单，等待货主确认';

  @override
  String get statusPendingContractSubtitle => '合同正在签署中，随时注意电话';

  @override
  String get statusPendingLoadSubtitle => '接单成功，尽快前往装货地装货吧';

  @override
  String get statusQueueingSubtitle => '排队中，耐心等待';

  @override
  String get statusTransportSubtitle => '开始运输了，记得每天报备位置哦';

  @override
  String get statusInClearanceSubtitle => '排队清关中，耐心等待';

  @override
  String get statusHeadingDestinationSubtitle => '正在前往目的地，运输中有问题请事件报备';

  @override
  String get statusPendingUnloadSubtitle => '已经到达目的地，请及时卸货哦';

  @override
  String get statusCompleteSubtitle => '订单已完成，感谢您一路护送';

  @override
  String get statusCancelledSubtitle => '订单已取消，如有问题请联系沟通哦';

  @override
  String get contactService => '沟通联系';

  @override
  String get orderInvalidStatus => '已失效';

  @override
  String get logs => '日志';

  @override
  String get weightPhoto => 'Сфотографировать и взвесить';

  @override
  String get weightPhotoPlaceholder => '请上传车辆过磅照片';

  @override
  String get finishLoad => '装货完成';

  @override
  String get activityReport => 'Сообщение о событии';

  @override
  String get locationNotAvailableTitle => '定位失败';

  @override
  String get locationNotAvailableMessage => '需要获取您的定位处理运输订单，请检查您的定位权限是否开启。';

  @override
  String get openSettings => '打开设置';

  @override
  String get acquiringLocation => '正在获取定位';

  @override
  String get viewPayment => '查看凭证';

  @override
  String get temporaryStay => '临时休息';

  @override
  String get temporaryStayPlaceholder => '请输入休息原因';

  @override
  String get unexpectedDelay => 'Особые задержки';

  @override
  String get unexpectedDelayPlaceholder => '请输入延误原因';

  @override
  String get selectReportItem => 'Выберите тип события';

  @override
  String get acquireLocationFailed => '定位失败，请稍后再试';

  @override
  String get dailyReport => 'Ежедневная регистрация';

  @override
  String get startClearance => '开始清关';

  @override
  String get endClearance => '清关完毕';

  @override
  String get arriveDestination => '到达目的地';

  @override
  String get unloadComplete => '卸货完毕';

  @override
  String get uploadReceipt => '上传交接单';

  @override
  String get uploadReceiptPlaceholder => '请上传交接单照片';

  @override
  String get cannotTakeOrderTitle => '无法接单';

  @override
  String get cannotTakeOrderMessage => '您目前有正在进行中的订单，请完成当前订单后再接新订单。';

  @override
  String get signOut => '退出登录';

  @override
  String get signOutMessage => '您正在退出账户，请确认是否退出。';

  @override
  String get deleteAccount => '注销账号';

  @override
  String get deleteAccountMessage => '您正在注销账户，注销后数据将清除，请确认。';

  @override
  String get accountDeletedMessage => '账号已注销，请联系客服。';

  @override
  String get unauthenticatedError => 'App认证失败，请从官方渠道下载App';

  @override
  String get onGoingOrder => '进行中订单';

  @override
  String get orderWeightPhotoMessage => '请您尽快前往装货地装货，记得一定要过磅拍照哈。';

  @override
  String get orderFinishLoadMessage => '您是否已经装车完毕，如果已经装货完毕，请点击装货完毕。';

  @override
  String get orderStartClearanceMessage => '您可能已经到了海关清关，请确认是否已经开始清关。';

  @override
  String get orderEndClearanceMessage => '您可能已经完成海关清关，请确认是否已完成清关。';

  @override
  String get orderArriveDestinationMessage => '您可能已经到达卸货地，请确认是否已到达目的地。';

  @override
  String get orderUploadReceiptMessage => '您可能已经卸货完成，请上传交接单。';

  @override
  String get orderPositionReportMessage => '您有进行中订单，请点击报备位置。';

  @override
  String get reportPosition => '报备位置';

  @override
  String get guestAccess => 'Guest Access';
}
