import 'package:driver/src/bank/view.dart';
import 'package:driver/src/mine/notifications.dart';
import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/driver/info.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/selectable.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/truck/view.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:driver/src/widgets/webview.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class MineView extends StatefulWidget {
  const MineView({super.key});

  @override
  State<StatefulWidget> createState() => _MineViewState();
}

class _MineViewState extends State<MineView> with EntryMixin, PageMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: backgroundColor,
        body: userProvider((user) => Column(children: [
              header(user),
              Expanded(
                  child: SingleChildScrollView(
                      child: Column(children: [
                verifyCard(user),
                driverCenter(user),
                serviceCenter(user),
                versionInfo()
              ])))
            ])));
  }

  void openDriverInfo() {
    User.shared.driverVerification != null
        ? Navigator.of(context).pushNamed(DriverInfoView.routeName)
        : LoginMixin.startUserLogin(context);
  }

  Widget header(User user) {
    CircleAvatar avatar;
    if (user.hasRegistered() && user.user?.photoURL != null) {
      avatar = CircleAvatar(
        backgroundColor: Color(0xFFD8D8D8),
        backgroundImage: NetworkImage(user.user!.photoURL!),
        radius: 28,
      );
    } else {
      avatar = CircleAvatar(
        backgroundColor: Color(0xFFD8D8D8),
        radius: 28,
        child: Icon(Icons.person, color: Colors.white, size: 48),
      );
    }
    Widget loginName;
    if (user.hasRegistered()) {
      Widget name;
      if (user.driverVerification?.name != null) {
        name = SizedBox(
            width: MediaQuery.of(context).size.width / 2,
            child: Text(user.driverVerification!.name,
                style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 24,
                    height: 1,
                    fontWeight: FontWeight.w500)));
      } else {
        name = Text(appLoc(context).completeInfo,
            style: TextStyle(
                decoration: TextDecoration.underline,
                decorationColor: primaryColor,
                color: primaryColor,
                fontSize: 24,
                height: 1,
                fontWeight: FontWeight.w500));
      }
      loginName = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          name,
          const SizedBox(height: 4),
          Text(user.user!.email ?? '')
        ],
      );
    } else {
      loginName = Text(appLoc(context).login,
          style: TextStyle(
              decoration: TextDecoration.underline,
              decorationColor: primaryColor,
              color: primaryColor,
              fontSize: 24,
              height: 1,
              fontWeight: FontWeight.w500));
    }
    Widget notification;
    if (user.hasRegistered()) {
      notification = notificationProvider((notif) => Stack(children: [
            IconButton(
                onPressed: () => Navigator.of(context)
                    .pushNamed(NotificationListView.routeName),
                icon: Icon(Icons.notifications_none, size: 24)),
            if (notif.hasUnread())
              Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                        color: Color(0xFFF2242B),
                        borderRadius: BorderRadius.circular(6)),
                  ))
          ]));
    } else {
      notification = SizedBox();
    }
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 24),
        alignment: Alignment.bottomCenter,
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).padding.top + 132,
        color: Color(0xFFEFFFFE),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              notification,
              InkWell(
                  onTap: openDriverInfo,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          avatar,
                          const SizedBox(width: 16),
                          loginName
                        ],
                      ),
                      Icon(Icons.arrow_forward_ios, size: 24)
                    ],
                  ))
            ]));
  }

  Widget iconButton(
      {required String image,
      required String title,
      void Function()? onTap,
      Widget? tag,
      double padding = 0,
      Color bgColor = Colors.transparent,
      Color textColor = const Color(0xFF333333)}) {
    double size = 44;
    double width = MediaQuery.of(context).size.width / 3 - 20;
    return InkWell(
        onTap: onTap,
        child: Stack(clipBehavior: Clip.none, children: [
          Column(children: [
            Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                    color: bgColor, borderRadius: BorderRadius.circular(8)),
                padding: EdgeInsets.all(padding),
                child: Image.asset(image, height: size, fit: BoxFit.fitHeight)),
            const SizedBox(height: 4),
            Container(
                alignment: Alignment.center,
                width: width,
                child: Text(title,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: textColor)))
          ]),
          if (tag != null) Positioned(top: 0, right: width / 2, child: tag)
        ]));
  }

  Widget verifyCard(User user) {
    if (user.driverVerification != null && user.truckVerifications.isNotEmpty) {
      return const SizedBox();
    }

    return InkWell(
        onTap: () => LoginMixin.startUserLogin(context),
        child: Container(
          width: MediaQuery.of(context).size.width - 20,
          margin: EdgeInsets.only(top: 10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                  fit: BoxFit.fitHeight,
                  image: AssetImage('assets/images/verify_card_bg.png'))),
          child: Stack(children: [
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                child: Column(children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                          width: MediaQuery.of(context).size.width * 0.6,
                          child: Text(
                            appLoc(context).verifyTitle,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w500),
                          )),
                      Row(
                        children: [
                          Container(
                              alignment: Alignment.centerRight,
                              width: MediaQuery.of(context).size.width * 0.2,
                              child: Text(
                                appLoc(context).verifyAction,
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    height: 1,
                                    fontWeight: FontWeight.w500),
                              )),
                          Padding(
                              padding: EdgeInsets.only(top: 1),
                              child: Icon(Icons.arrow_forward_ios,
                                  color: Colors.white, size: 14))
                        ],
                      )
                    ],
                  ),
                  Container(
                      decoration:
                          BoxDecoration(borderRadius: BorderRadius.circular(4)),
                      padding: EdgeInsets.all(10),
                      margin: EdgeInsets.only(top: 10),
                      child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            iconButton(
                                image: 'assets/images/ic_order.png',
                                title: appLoc(context).massiveOrders,
                                padding: 10,
                                bgColor: Color(0xFF74C5CF),
                                textColor: Color(0xB3FFFFFF)),
                            iconButton(
                                image: 'assets/images/ic_pay.png',
                                title: appLoc(context).fastPay,
                                padding: 10,
                                bgColor: Color(0xFF74C5CF),
                                textColor: Color(0xB3FFFFFF)),
                            iconButton(
                                image: 'assets/images/ic_safe.png',
                                title: appLoc(context).safe,
                                padding: 10,
                                bgColor: Color(0xFF74C5CF),
                                textColor: Color(0xB3FFFFFF))
                          ]))
                ]))
          ]),
        ));
  }

  Widget? driverStatusTag(User user) {
    String? status = user.unverifiedDriverStatus();
    if (status != null) {
      return Container(
          constraints:
              BoxConstraints(maxWidth: MediaQuery.of(context).size.width / 6),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          decoration: BoxDecoration(
              color: user.driverVerification == null ||
                      user.driverVerification?.status ==
                          VerificationStatus.pending
                  ? warningColor
                  : errorColor,
              borderRadius: BorderRadius.circular(4)),
          child:
              Text(status, style: TextStyle(color: Colors.white, fontSize: 9)));
    }

    return null;
  }

  Widget? truckStatusTag(User user) {
    String? status;
    Color color = warningColor;
    if (user.truckVerifications.isEmpty) {
      status = appLoc(context).verificationEmpty;
    } else if (user.truckVerifications
        .where((e) => e.status == VerificationStatus.pending)
        .isNotEmpty) {
      status = appLoc(context).verificationPending;
    } else if (user.truckVerifications
        .where((e) => e.status == VerificationStatus.rejected)
        .isNotEmpty) {
      status = appLoc(context).verificationRejected;
      color = errorColor;
    }

    if (status != null) {
      return Container(
          constraints:
              BoxConstraints(maxWidth: MediaQuery.of(context).size.width / 6),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          decoration: BoxDecoration(
              color: color, borderRadius: BorderRadius.circular(4)),
          child:
              Text(status, style: TextStyle(color: Colors.white, fontSize: 9)));
    }

    return null;
  }

  Widget driverCenter(User user) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
      width: MediaQuery.of(context).size.width - 20,
      margin: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(4)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            appLoc(context).driverCenter,
            style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333)),
          ),
          const SizedBox(height: 10),
          Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                iconButton(
                    image: 'assets/images/ic_driver.png',
                    title: appLoc(context).driverVerify,
                    onTap: openDriverInfo,
                    tag: driverStatusTag(user)),
                iconButton(
                    image: 'assets/images/ic_truck.png',
                    title: appLoc(context).truckManagement,
                    tag: truckStatusTag(user),
                    onTap: () => Navigator.of(context)
                        .pushNamed(TruckManagementView.routeName)),
                iconButton(
                    image: 'assets/images/ic_bank.png',
                    title: appLoc(context).bankInfo,
                    onTap: () {
                      if (User.shared.hasRegistered()) {
                        Navigator.of(context)
                            .pushNamed(BankManagementView.routeName);
                      } else {
                        LoginMixin.startUserLogin(context);
                      }
                    }),
              ])
        ],
      ),
    );
  }

  Widget serviceRow(
      {void Function()? onTap, required String image, required String title}) {
    return InkWell(
        onTap: onTap,
        child: Padding(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Image.asset(image, height: 24, fit: BoxFit.fitHeight),
                    const SizedBox(width: 8),
                    Text(title,
                        style:
                            TextStyle(fontSize: 16, color: Color(0xFF333333)))
                  ],
                ),
                Padding(
                    padding: EdgeInsets.only(top: 1),
                    child: Icon(Icons.arrow_forward_ios,
                        color: Color(0xFF999999), size: 14))
              ],
            )));
  }

  Widget serviceCenter(User user) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 16),
        width: MediaQuery.of(context).size.width - 20,
        margin: EdgeInsets.only(top: 10),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(4)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            appLoc(context).serviceCenter,
            style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333)),
          ),
          const SizedBox(height: 16),
          serviceRow(
              image: 'assets/images/ic_phone.png',
              title: appLoc(context).customerService,
              onTap: customerService),
          serviceRow(
              image: 'assets/images/ic_guarantee_agreement.png',
              title: appLoc(context).guarantee,
              onTap: () => WebView.open(context, WebResource.information)),
          serviceRow(
              image: 'assets/images/ic_user_agreement.png',
              title: appLoc(context).userAgreementAction,
              onTap: () => WebView.open(context, WebResource.agreement)),
          serviceRow(
              image: 'assets/images/ic_privacy.png',
              title: appLoc(context).privacyAgreementAction,
              onTap: () => WebView.open(context, WebResource.privacy)),
          serviceRow(
              image: 'assets/images/ic_lang.png',
              title: appLoc(context).switchLanguage,
              onTap: () async {
                final options = LanguageOption.allLanguages();
                int currentIdx = options.indexOf(options.firstWhere((e) =>
                    e.locale.languageCode ==
                    Localizations.localeOf(context).languageCode));
                final res = await showSelectionDialog(
                    options: options,
                    title: appLoc(context).switchLanguage,
                    initialSelectIndex: currentIdx,
                    cancelText: appLoc(context).cancel);
                if (res != null) {
                  SettingsController.shared
                      .updateLocale(res.locale.languageCode);
                }
              }),
          if (user.hasRegistered())
            serviceRow(
                image: 'assets/images/ic_signout.png',
                title: appLoc(context).signOut,
                onTap: signOut),
          if (user.hasRegistered())
            serviceRow(
                image: 'assets/images/ic_delete_account.png',
                title: appLoc(context).deleteAccount,
                onTap: deleteAccount),
        ]));
  }

  Widget versionInfo() {
    return Container(
        alignment: Alignment.bottomRight,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(top: 8, right: 12),
        child: Text(
          'v${Device.shared.version}+${Device.shared.buildNum}',
          style: TextStyle(fontSize: 12, color: Color(0xFF999999)),
        ));
  }

  Future signOut() async {
    final res = await showAppDialog(
        context, dialogMessage(message: appLoc(context).signOutMessage), [
      dialogButton(context, appLoc(context).confirm, DialogButtonType.cancel),
      dialogButton(context, appLoc(context).cancel, DialogButtonType.primary)
    ]);
    if (res == DialogButtonType.cancel) {
      await User.shared.performLogout();
    }
  }

  Future deleteAccount() async {
    final res = await showAppDialog(
        context, dialogMessage(message: appLoc(context).deleteAccountMessage), [
      dialogButton(context, appLoc(context).confirm, DialogButtonType.cancel),
      dialogButton(context, appLoc(context).cancel, DialogButtonType.primary)
    ]);
    if (res == DialogButtonType.cancel) {
      await User.shared.deleteAccount();
    }
  }
}
