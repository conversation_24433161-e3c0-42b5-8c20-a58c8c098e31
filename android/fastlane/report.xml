<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.00032">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: flutter clean" time="7.309285">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: flutter packages pub get" time="2.093338">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: flutter build appbundle --release" time="133.615464">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: upload_to_play_store" time="38.70431">
        
          <failure message="/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in `execute_action'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:255:in `block in execute_action'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:229:in `chdir'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:229:in `execute_action'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:157:in `trigger_action_by_name'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/fast_file.rb:159:in `method_missing'&#10;Fastfile:27:in `block (2 levels) in parsing_binding'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/lane.rb:41:in `call'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:49:in `block in execute'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:45:in `chdir'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/runner.rb:45:in `execute'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/lane_manager.rb:46:in `cruise_lane'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/command_line_handler.rb:34:in `handle'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:110:in `block (2 levels) in run'&#10;/Users/<USER>/.gem/gems/commander-4.6.0/lib/commander/command.rb:187:in `call'&#10;/Users/<USER>/.gem/gems/commander-4.6.0/lib/commander/command.rb:157:in `run'&#10;/Users/<USER>/.gem/gems/commander-4.6.0/lib/commander/runner.rb:444:in `run_active_command'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:124:in `run!'&#10;/Users/<USER>/.gem/gems/commander-4.6.0/lib/commander/delegates.rb:18:in `run!'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:363:in `run'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/commands_generator.rb:43:in `start'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/fastlane/lib/fastlane/cli_tools_distributor.rb:123:in `take_off'&#10;/Users/<USER>/.gem/gems/fastlane-2.228.0/bin/fastlane:23:in `&lt;top (required)&gt;'&#10;/usr/local/bin/fastlane:23:in `load'&#10;/usr/local/bin/fastlane:23:in `&lt;main&gt;'&#10;&#10;Google Api Error: Invalid request - Only releases with status draft may be created on draft app." />
        
      </testcase>
    
  </testsuite>
</testsuites>
