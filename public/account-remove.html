<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号删除请求 - GoFreight司机端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #029FB0, #0288a3);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .section {
            margin-bottom: 35px;
        }

        .section h2 {
            color: #029FB0;
            font-size: 1.5em;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }

        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .step:last-child {
            margin-bottom: 0;
        }

        .step-number {
            background: #029FB0;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .step-description {
            color: #666;
            font-size: 0.95em;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .highlight strong {
            color: #856404;
        }

        .contact-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .contact-info h3 {
            color: #1565c0;
            margin-bottom: 10px;
        }

        .contact-info p {
            margin-bottom: 8px;
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }

        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
        }

        .language-selector select {
            background: transparent;
            border: none;
            color: white;
            font-size: 14px;
        }

        .language-selector select option {
            color: #333;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 30px 20px;
            }

            .step {
                flex-direction: column;
                text-align: center;
            }

            .step-number {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .language-selector {
                position: static;
                margin-bottom: 20px;
                background: rgba(255, 255, 255, 0.3);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="language-selector">
                <select onchange="changeLanguage(this.value)">
                    <option value="zh">中文</option>
                    <option value="en">English</option>
                    <option value="ru">Русский</option>
                    <option value="kk">Қазақша</option>
                </select>
            </div>
            <h1 id="page-title">账号删除请求</h1>
            <p id="page-subtitle">GoFreight司机端应用 - 由Mashina开发</p>
        </div>

        <div class="content">
            <div class="section">
                <h2 id="overview-title">概述</h2>
                <p id="overview-text">
                    根据用户数据保护法规，您有权要求删除您在GoFreight司机端应用中的个人账号和相关数据。
                    本页面将指导您完成账号删除请求的流程，并说明哪些数据将被删除或保留。
                </p>
            </div>

            <div class="section">
                <h2 id="steps-title">账号删除步骤</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title" id="step1-title">打开GoFreight司机端应用</div>
                            <div class="step-description" id="step1-desc">在您的移动设备上启动GoFreight司机端应用</div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title" id="step2-title">进入个人设置</div>
                            <div class="step-description" id="step2-desc">点击应用底部导航栏的"我的"或"设置"选项</div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title" id="step3-title">找到账号管理</div>
                            <div class="step-description" id="step3-desc">在设置页面中找到"账号管理"或"隐私设置"选项</div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title" id="step4-title">申请删除账号</div>
                            <div class="step-description" id="step4-desc">点击"删除账号"选项，确认您的删除请求</div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <div class="step-title" id="step5-title">联系客服（备选方案）</div>
                            <div class="step-description" id="step5-desc">如果应用内无法完成删除，请通过下方联系方式联系我们的客服团队</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 id="data-title">数据处理说明</h2>

                <div class="highlight">
                    <strong id="processing-time">处理时间：</strong>
                    <span id="processing-desc">我们将在收到您的删除请求后30天内完成账号删除处理。</span>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th id="data-type-header">数据类型</th>
                            <th id="action-header">处理方式</th>
                            <th id="retention-header">保留期限</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td id="personal-info">个人身份信息</td>
                            <td id="personal-action">立即删除</td>
                            <td id="personal-retention">无</td>
                        </tr>
                        <tr>
                            <td id="profile-data">用户资料数据</td>
                            <td id="profile-action">立即删除</td>
                            <td id="profile-retention">无</td>
                        </tr>
                        <tr>
                            <td id="location-data">位置信息</td>
                            <td id="location-action">立即删除</td>
                            <td id="location-retention">无</td>
                        </tr>
                        <tr>
                            <td id="order-history">订单历史记录</td>
                            <td id="order-action">匿名化处理</td>
                            <td id="order-retention">7年（法律要求）</td>
                        </tr>
                        <tr>
                            <td id="financial-records">财务交易记录</td>
                            <td id="financial-action">匿名化处理</td>
                            <td id="financial-retention">7年（法律要求）</td>
                        </tr>
                        <tr>
                            <td id="chat-messages">聊天消息</td>
                            <td id="chat-action">立即删除</td>
                            <td id="chat-retention">无</td>
                        </tr>
                        <tr>
                            <td id="device-info">设备信息</td>
                            <td id="device-action">立即删除</td>
                            <td id="device-retention">无</td>
                        </tr>
                    </tbody>
                </table>

                <div class="highlight">
                    <strong id="legal-note">法律说明：</strong>
                    <span id="legal-desc">
                        某些数据（如财务交易记录）根据当地法律法规需要保留特定期限，这些数据将进行匿名化处理，
                        无法追溯到您的个人身份。
                    </span>
                </div>
            </div>

            <div class="section">
                <h2 id="contact-title">联系我们</h2>
                <div class="contact-info">
                    <h3 id="support-title">客服支持</h3>
                    <p id="phone-label"><strong>电话：</strong> +77071742905</p>
                    <p id="email-label"><strong>邮箱：</strong> <EMAIL></p>
                    <p id="hours-label"><strong>服务时间：</strong> <span id="hours-text">周一至周五 9:00-18:00 (UTC+8)</span></p>
                </div>

                <p id="contact-note">
                    如果您在使用应用内删除功能时遇到任何问题，或需要进一步的帮助，
                    请通过上述方式联系我们的客服团队。我们将协助您完成账号删除流程。
                </p>
            </div>
        </div>

        <div class="footer">
            <p id="footer-text">
                © 2024 Mashina. GoFreight司机端应用。保留所有权利。<br>
                本页面符合Google Play商店账号删除要求。
            </p>
        </div>
    </div>

    <script>
        const translations = {
            zh: {
                'page-title': '账号删除请求',
                'page-subtitle': 'GoFreight司机端应用 - 由Mashina开发',
                'overview-title': '概述',
                'overview-text': '根据用户数据保护法规，您有权要求删除您在GoFreight司机端应用中的个人账号和相关数据。本页面将指导您完成账号删除请求的流程，并说明哪些数据将被删除或保留。',
                'steps-title': '账号删除步骤',
                'step1-title': '打开GoFreight司机端应用',
                'step1-desc': '在您的移动设备上启动GoFreight司机端应用',
                'step2-title': '进入个人设置',
                'step2-desc': '点击应用底部导航栏的"我的"或"设置"选项',
                'step3-title': '找到账号管理',
                'step3-desc': '在设置页面中找到"账号管理"或"隐私设置"选项',
                'step4-title': '申请删除账号',
                'step4-desc': '点击"删除账号"选项，确认您的删除请求',
                'step5-title': '联系客服（备选方案）',
                'step5-desc': '如果应用内无法完成删除，请通过下方联系方式联系我们的客服团队',
                'data-title': '数据处理说明',
                'processing-time': '处理时间：',
                'processing-desc': '我们将在收到您的删除请求后30天内完成账号删除处理。',
                'data-type-header': '数据类型',
                'action-header': '处理方式',
                'retention-header': '保留期限',
                'personal-info': '个人身份信息',
                'personal-action': '立即删除',
                'personal-retention': '无',
                'profile-data': '用户资料数据',
                'profile-action': '立即删除',
                'profile-retention': '无',
                'location-data': '位置信息',
                'location-action': '立即删除',
                'location-retention': '无',
                'order-history': '订单历史记录',
                'order-action': '匿名化处理',
                'order-retention': '7年（法律要求）',
                'financial-records': '财务交易记录',
                'financial-action': '匿名化处理',
                'financial-retention': '7年（法律要求）',
                'chat-messages': '聊天消息',
                'chat-action': '立即删除',
                'chat-retention': '无',
                'device-info': '设备信息',
                'device-action': '立即删除',
                'device-retention': '无',
                'legal-note': '法律说明：',
                'legal-desc': '某些数据（如财务交易记录）根据当地法律法规需要保留特定期限，这些数据将进行匿名化处理，无法追溯到您的个人身份。',
                'contact-title': '联系我们',
                'support-title': '客服支持',
                'phone-label': '电话：',
                'email-label': '邮箱：',
                'hours-label': '服务时间：',
                'hours-text': '周一至周五 9:00-18:00 (UTC+8)',
                'contact-note': '如果您在使用应用内删除功能时遇到任何问题，或需要进一步的帮助，请通过上述方式联系我们的客服团队。我们将协助您完成账号删除流程。',
                'footer-text': '© 2024 Mashina. GoFreight司机端应用。保留所有权利。<br>本页面符合Google Play商店账号删除要求。'
            },
            en: {
                'page-title': 'Account Deletion Request',
                'page-subtitle': 'GoFreight Driver App - Developed by Mashina',
                'overview-title': 'Overview',
                'overview-text': 'According to user data protection regulations, you have the right to request deletion of your personal account and related data in the GoFreight Driver app. This page will guide you through the account deletion request process and explain which data will be deleted or retained.',
                'steps-title': 'Account Deletion Steps',
                'step1-title': 'Open GoFreight Driver App',
                'step1-desc': 'Launch the GoFreight Driver app on your mobile device',
                'step2-title': 'Go to Personal Settings',
                'step2-desc': 'Tap "Profile" or "Settings" option in the bottom navigation bar',
                'step3-title': 'Find Account Management',
                'step3-desc': 'Look for "Account Management" or "Privacy Settings" option in the settings page',
                'step4-title': 'Request Account Deletion',
                'step4-desc': 'Tap "Delete Account" option and confirm your deletion request',
                'step5-title': 'Contact Support (Alternative)',
                'step5-desc': 'If you cannot complete deletion within the app, please contact our support team using the contact information below',
                'data-title': 'Data Processing Information',
                'processing-time': 'Processing Time:',
                'processing-desc': 'We will complete account deletion processing within 30 days of receiving your deletion request.',
                'data-type-header': 'Data Type',
                'action-header': 'Processing Method',
                'retention-header': 'Retention Period',
                'personal-info': 'Personal Identity Information',
                'personal-action': 'Immediate Deletion',
                'personal-retention': 'None',
                'profile-data': 'User Profile Data',
                'profile-action': 'Immediate Deletion',
                'profile-retention': 'None',
                'location-data': 'Location Information',
                'location-action': 'Immediate Deletion',
                'location-retention': 'None',
                'order-history': 'Order History',
                'order-action': 'Anonymized',
                'order-retention': '7 years (Legal Requirement)',
                'financial-records': 'Financial Transaction Records',
                'financial-action': 'Anonymized',
                'financial-retention': '7 years (Legal Requirement)',
                'chat-messages': 'Chat Messages',
                'chat-action': 'Immediate Deletion',
                'chat-retention': 'None',
                'device-info': 'Device Information',
                'device-action': 'Immediate Deletion',
                'device-retention': 'None',
                'legal-note': 'Legal Notice:',
                'legal-desc': 'Certain data (such as financial transaction records) must be retained for specific periods according to local laws and regulations. This data will be anonymized and cannot be traced back to your personal identity.',
                'contact-title': 'Contact Us',
                'support-title': 'Customer Support',
                'phone-label': 'Phone:',
                'email-label': 'Email:',
                'hours-label': 'Service Hours:',
                'hours-text': 'Monday to Friday 9:00-18:00 (UTC+8)',
                'contact-note': 'If you encounter any issues using the in-app deletion feature or need further assistance, please contact our support team using the methods above. We will help you complete the account deletion process.',
                'footer-text': '© 2024 Mashina. GoFreight Driver App. All rights reserved.<br>This page complies with Google Play Store account deletion requirements.'
            },
            ru: {
                'page-title': 'Запрос на удаление аккаунта',
                'page-subtitle': 'Приложение GoFreight для водителей - Разработано Mashina',
                'overview-title': 'Обзор',
                'overview-text': 'Согласно правилам защиты пользовательских данных, вы имеете право запросить удаление вашего личного аккаунта и связанных данных в приложении GoFreight для водителей. Эта страница поможет вам пройти процесс запроса удаления аккаунта и объяснит, какие данные будут удалены или сохранены.',
                'steps-title': 'Шаги удаления аккаунта',
                'step1-title': 'Откройте приложение GoFreight для водителей',
                'step1-desc': 'Запустите приложение GoFreight для водителей на вашем мобильном устройстве',
                'step2-title': 'Перейдите в личные настройки',
                'step2-desc': 'Нажмите на опцию "Профиль" или "Настройки" в нижней панели навигации',
                'step3-title': 'Найдите управление аккаунтом',
                'step3-desc': 'Найдите опцию "Управление аккаунтом" или "Настройки конфиденциальности" на странице настроек',
                'step4-title': 'Запросите удаление аккаунта',
                'step4-desc': 'Нажмите на опцию "Удалить аккаунт" и подтвердите ваш запрос на удаление',
                'step5-title': 'Свяжитесь с поддержкой (альтернатива)',
                'step5-desc': 'Если вы не можете завершить удаление в приложении, свяжитесь с нашей службой поддержки, используя контактную информацию ниже',
                'data-title': 'Информация об обработке данных',
                'processing-time': 'Время обработки:',
                'processing-desc': 'Мы завершим обработку удаления аккаунта в течение 30 дней после получения вашего запроса на удаление.',
                'data-type-header': 'Тип данных',
                'action-header': 'Метод обработки',
                'retention-header': 'Период хранения',
                'personal-info': 'Личная идентификационная информация',
                'personal-action': 'Немедленное удаление',
                'personal-retention': 'Нет',
                'profile-data': 'Данные профиля пользователя',
                'profile-action': 'Немедленное удаление',
                'profile-retention': 'Нет',
                'location-data': 'Информация о местоположении',
                'location-action': 'Немедленное удаление',
                'location-retention': 'Нет',
                'order-history': 'История заказов',
                'order-action': 'Анонимизация',
                'order-retention': '7 лет (законное требование)',
                'financial-records': 'Записи финансовых транзакций',
                'financial-action': 'Анонимизация',
                'financial-retention': '7 лет (законное требование)',
                'chat-messages': 'Сообщения чата',
                'chat-action': 'Немедленное удаление',
                'chat-retention': 'Нет',
                'device-info': 'Информация об устройстве',
                'device-action': 'Немедленное удаление',
                'device-retention': 'Нет',
                'legal-note': 'Правовое уведомление:',
                'legal-desc': 'Определенные данные (такие как записи финансовых транзакций) должны храниться в течение определенных периодов согласно местным законам и правилам. Эти данные будут анонимизированы и не могут быть прослежены до вашей личности.',
                'contact-title': 'Свяжитесь с нами',
                'support-title': 'Служба поддержки клиентов',
                'phone-label': 'Телефон:',
                'email-label': 'Электронная почта:',
                'hours-label': 'Часы работы:',
                'hours-text': 'Понедельник - пятница 9:00-18:00 (UTC+8)',
                'contact-note': 'Если у вас возникли проблемы с использованием функции удаления в приложении или вам нужна дополнительная помощь, свяжитесь с нашей службой поддержки, используя указанные выше методы. Мы поможем вам завершить процесс удаления аккаунта.',
                'footer-text': '© 2024 Mashina. Приложение GoFreight для водителей. Все права защищены.<br>Эта страница соответствует требованиям Google Play Store для удаления аккаунта.'
            },
            kk: {
                'page-title': 'Аккаунтты жою сұрауы',
                'page-subtitle': 'GoFreight жүргізуші қолданбасы - Mashina дамытқан',
                'overview-title': 'Шолу',
                'overview-text': 'Пайдаланушы деректерін қорғау ережелеріне сәйкес, сізде GoFreight жүргізуші қолданбасындағы жеке аккаунтыңыз бен байланысты деректерді жоюды сұрау құқығы бар. Бұл бет аккаунтты жою сұрауы процесін басшылық етеді және қандай деректер жойылатынын немесе сақталатынын түсіндіреді.',
                'steps-title': 'Аккаунтты жою қадамдары',
                'step1-title': 'GoFreight жүргізуші қолданбасын ашыңыз',
                'step1-desc': 'Мобильді құрылғыңызда GoFreight жүргізуші қолданбасын іске қосыңыз',
                'step2-title': 'Жеке баптауларға өтіңіз',
                'step2-desc': 'Төменгі навигация тақтасындағы "Профиль" немесе "Баптаулар" опциясын басыңыз',
                'step3-title': 'Аккаунт басқаруын табыңыз',
                'step3-desc': 'Баптаулар бетінде "Аккаунт басқару" немесе "Құпиялылық баптаулары" опциясын табыңыз',
                'step4-title': 'Аккаунтты жоюды сұраңыз',
                'step4-desc': '"Аккаунтты жою" опциясын басып, жою сұрауыңызды растаңыз',
                'step5-title': 'Қолдау қызметіне хабарласыңыз (балама)',
                'step5-desc': 'Егер қолданба ішінде жоюды аяқтай алмасаңыз, төмендегі байланыс ақпаратын пайдаланып қолдау командамызға хабарласыңыз',
                'data-title': 'Деректерді өңдеу туралы ақпарат',
                'processing-time': 'Өңдеу уақыты:',
                'processing-desc': 'Біз сіздің жою сұрауыңызды алғаннан кейін 30 күн ішінде аккаунтты жою өңдеуін аяқтаймыз.',
                'data-type-header': 'Деректер түрі',
                'action-header': 'Өңдеу әдісі',
                'retention-header': 'Сақтау мерзімі',
                'personal-info': 'Жеке сәйкестендіру ақпараты',
                'personal-action': 'Лезде жою',
                'personal-retention': 'Жоқ',
                'profile-data': 'Пайдаланушы профилінің деректері',
                'profile-action': 'Лезде жою',
                'profile-retention': 'Жоқ',
                'location-data': 'Орналасу ақпараты',
                'location-action': 'Лезде жою',
                'location-retention': 'Жоқ',
                'order-history': 'Тапсырыс тарихы',
                'order-action': 'Анонимдеу',
                'order-retention': '7 жыл (заңды талап)',
                'financial-records': 'Қаржылық транзакция жазбалары',
                'financial-action': 'Анонимдеу',
                'financial-retention': '7 жыл (заңды талап)',
                'chat-messages': 'Чат хабарлары',
                'chat-action': 'Лезде жою',
                'chat-retention': 'Жоқ',
                'device-info': 'Құрылғы ақпараты',
                'device-action': 'Лезде жою',
                'device-retention': 'Жоқ',
                'legal-note': 'Заңды ескерту:',
                'legal-desc': 'Кейбір деректер (қаржылық транзакция жазбалары сияқты) жергілікті заңдар мен ережелерге сәйкес белгілі мерзімдерде сақталуы керек. Бұл деректер анонимделеді және сіздің жеке басыңызға дейін іздеу мүмкін емес.',
                'contact-title': 'Бізбен байланысыңыз',
                'support-title': 'Тұтынушыларды қолдау',
                'phone-label': 'Телефон:',
                'email-label': 'Электрондық пошта:',
                'hours-label': 'Қызмет уақыты:',
                'hours-text': 'Дүйсенбі - жұма 9:00-18:00 (UTC+8)',
                'contact-note': 'Егер қолданба ішіндегі жою функциясын пайдаланғанда проблемалар туындаса немесе қосымша көмек қажет болса, жоғарыда көрсетілген әдістерді пайдаланып қолдау командамызға хабарласыңыз. Біз аккаунтты жою процесін аяқтауға көмектесеміз.',
                'footer-text': '© 2024 Mashina. GoFreight жүргізуші қолданбасы. Барлық құқықтар қорғалған.<br>Бұл бет Google Play Store аккаунтты жою талаптарына сәйкес келеді.'
            }
        };

        function changeLanguage(lang) {
            const translation = translations[lang];
            if (!translation) return;

            Object.keys(translation).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.innerHTML = translation[key];
                }
            });

            // Update HTML lang attribute
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' :
                                          lang === 'en' ? 'en' :
                                          lang === 'ru' ? 'ru' : 'kk';
        }

        // Set default language based on browser preference
        document.addEventListener('DOMContentLoaded', function() {
            const browserLang = navigator.language || navigator.userLanguage;
            let defaultLang = 'zh'; // Default to Chinese

            if (browserLang.startsWith('en')) defaultLang = 'en';
            else if (browserLang.startsWith('ru')) defaultLang = 'ru';
            else if (browserLang.startsWith('kk')) defaultLang = 'kk';

            const selector = document.querySelector('.language-selector select');
            selector.value = defaultLang;
            changeLanguage(defaultLang);
        });
    </script>
</body>
</html>