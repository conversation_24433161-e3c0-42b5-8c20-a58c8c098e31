MIME-version: 1.0
Content-Type: multipart/related;
	boundary="----=_NextPart_000_0076_01C29953.BE473C30";
	type="text/html"
X-MimeOLE: Produced By Microsoft MimeOLE V6.00.2800.1106

This is a multi-part message in MIME format.

------=_NextPart_000_0076_01C29953.BE473C30
Content-Type: text/html; 
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///C:/Users/<USER>/AppData/Local/Temp/~tmp{7ce5ea00-38ef-48df-be93-ac39d18253c6}1509938.TMP.html

<html xmlns:o=3D"urn:schemas-microsoft-com:office:office" xmlns:w=3D"urn:schemas-microsoft-com:office:word" xmlns:dt=3D"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns=3D"http://www.w3.org/TR/REC-html40"><head><meta http-equiv=3DContent-Type  content=3D"text/html; charset=3Dutf-8" ><meta name=3DProgId  content=3DWord.Document ><meta name=3DGenerator  content=3D"Microsoft Word 14" ><meta name=3DOriginator  content=3D"Microsoft Word 14" ><title></title><!--[if gte mso 9]><xml><o:DocumentProperties><o:Author>Lycon</o:Author><o:LastAuthor>觞.无痕</o:LastAuthor><o:Revision>1</o:Revision><o:Pages>2</o:Pages><o:Characters>902</o:Characters><o:Lines>1</o:Lines><o:Paragraphs>1</o:Paragraphs></o:DocumentProperties><o:CustomDocumentProperties><o:KSOProductBuildVer dt:dt=3D"string" >2052-12.1.0.22529</o:KSOProductBuildVer><o:ICV dt:dt=3D"string" >A9B1B6AD39CD4D53A94DD26816EA3DD2_13</o:ICV><o:KSOTemplateDocerSaveRecord dt:dt=3D"string" >eyJoZGlkIjoiNzQ3YzU1NjQ4NzMzMjg2YTE1OWEyNmVlZGY0ZGM2OTEiLCJ1c2VySWQiOiIxMDY3ODAxMTczIn0=3D</o:KSOTemplateDocerSaveRecord></o:CustomDocumentProperties></xml><![endif]--><!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]--><!--[if gte mso 9]><xml><w:WordDocument><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery><w:DocumentKind>DocumentNotSpecified</w:DocumentKind><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing><w:PunctuationKerning></w:PunctuationKerning><w:View>Web</w:View><w:Compatibility><w:AdjustLineHeightInTable/><w:DontGrowAutofit/><w:BalanceSingleByteDoubleByteWidth/><w:DoNotExpandShiftReturn/><w:UseFELayout/></w:Compatibility><w:Zoom>0</w:Zoom></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml><w:LatentStyles DefLockedState=3D"false"  DefUnhideWhenUsed=3D"true"  DefSemiHidden=3D"true"  DefQFormat=3D"false"  DefPriority=3D"99"  LatentStyleCount=3D"260" >
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Normal" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"heading 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 7" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 8" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"heading 9" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 7" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 8" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index 9" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 7" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 8" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toc 9" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Normal Indent" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"footnote text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"annotation text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"header" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"footer" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"index heading" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  QFormat=3D"true"  Name=3D"caption" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"table of figures" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"envelope address" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"envelope return" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"footnote reference" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"annotation reference" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"line number" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"page number" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"endnote reference" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"endnote text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"table of authorities" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"macro" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"toa heading" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Bullet" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Number" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Bullet 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Bullet 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Bullet 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Bullet 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Number 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Number 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Number 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Number 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Title" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Closing" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Signature" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Default Paragraph Font" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text Indent" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Continue" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Continue 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Continue 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Continue 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"List Continue 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Message Header" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Subtitle" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Salutation" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Date" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text First Indent" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text First Indent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Note Heading" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text Indent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Body Text Indent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Block Text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Hyperlink" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"FollowedHyperlink" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Strong" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Emphasis" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Document Map" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Plain Text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"E-mail Signature" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Normal (Web)" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Acronym" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Address" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Cite" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Code" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Definition" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Keyboard" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Preformatted" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Sample" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Typewriter" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"HTML Variable" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Normal Table" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"annotation subject" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"No List" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"1 / a / i" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"1 / 1.1 / 1.1.1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"Article / Section" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Simple 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Simple 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Simple 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Classic 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Classic 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Classic 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Classic 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Colorful 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Colorful 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Colorful 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Columns 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Columns 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Columns 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Columns 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Columns 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 7" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Grid 8" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 7" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table List 8" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table 3D effects 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table 3D effects 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table 3D effects 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Contemporary" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Elegant" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Professional" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Subtle 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Subtle 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Web 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Web 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Web 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Balloon Text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  QFormat=3D"true"  Name=3D"Table Grid" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"0"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Table Theme" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"Placeholder Text" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"No Spacing" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"List Paragraph" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"Quote" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"99"  SemiHidden=3D"false"  Name=3D"Intense Quote" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 1" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 2" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 3" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 4" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 5" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"60"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Shading Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"61"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light List Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"62"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Light Grid Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"63"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 1 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"64"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Shading 2 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"65"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 1 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"66"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium List 2 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"67"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 1 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"68"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 2 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"69"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Medium Grid 3 Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"70"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Dark List Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"71"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Shading Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"72"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful List Accent 6" ></w:LsdException>
=20<w:LsdException Locked=3D"false"  Priority=3D"73"  SemiHidden=3D"false"  UnhideWhenUsed=3D"false"  Name=3D"Colorful Grid Accent 6" ></w:LsdException>
=20</w:LatentStyles></xml><![endif]--><style>
=20@font-face{
=20font-family:"Times New Roman";
=20}
=20
=20@font-face{
=20font-family:"宋体";
=20}
=20
=20@font-face{
=20font-family:"Wingdings";
=20}
=20
=20@font-face{
=20font-family:"Symbol";
=20}
=20
=20@font-face{
=20font-family:"Calibri";
=20}
=20
=20@list l0:level1{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:36.0000pt;
=20mso-level-number-position:left;
=20margin-left:36.0000pt;text-indent:-18.0000pt;tab-stops:blank 36.0000pt ;font-family:Symbol;font-size:10.0000pt;}
=20
=20@list l0:level2{
=20mso-level-number-format:bullet;
=20mso-level-suffix:space;
=20mso-level-text:"";
=20mso-level-tab-stop:none;
=20mso-level-number-position:left;
=20margin-left:72.0000pt;text-indent:-18.0000pt;font-family:Symbol;font-size:10.0000pt;}
=20
=20@list l0:level3{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:108.0000pt;
=20mso-level-number-position:left;
=20margin-left:108.0000pt;text-indent:-18.0000pt;tab-stops:blank 108.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level4{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:144.0000pt;
=20mso-level-number-position:left;
=20margin-left:144.0000pt;text-indent:-18.0000pt;tab-stops:blank 144.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level5{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:180.0000pt;
=20mso-level-number-position:left;
=20margin-left:180.0000pt;text-indent:-18.0000pt;tab-stops:blank 180.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level6{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:216.0000pt;
=20mso-level-number-position:left;
=20margin-left:216.0000pt;text-indent:-18.0000pt;tab-stops:blank 216.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level7{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:252.0000pt;
=20mso-level-number-position:left;
=20margin-left:252.0000pt;text-indent:-18.0000pt;tab-stops:blank 252.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level8{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:288.0000pt;
=20mso-level-number-position:left;
=20margin-left:288.0000pt;text-indent:-18.0000pt;tab-stops:blank 288.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l0:level9{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:324.0000pt;
=20mso-level-number-position:left;
=20margin-left:324.0000pt;text-indent:-18.0000pt;tab-stops:blank 324.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level1{
=20mso-level-number-format:bullet;
=20mso-level-suffix:space;
=20mso-level-text:"";
=20mso-level-tab-stop:none;
=20mso-level-number-position:left;
=20margin-left:57.0000pt;text-indent:-18.0000pt;font-family:Symbol;font-size:10.0000pt;}
=20
=20@list l1:level2{
=20mso-level-number-format:bullet;
=20mso-level-suffix:space;
=20mso-level-text:"";
=20mso-level-tab-stop:none;
=20mso-level-number-position:left;
=20margin-left:93.0000pt;text-indent:-18.0000pt;font-family:Symbol;font-size:10.0000pt;}
=20
=20@list l1:level3{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:129.0000pt;
=20mso-level-number-position:left;
=20margin-left:129.0000pt;text-indent:-18.0000pt;tab-stops:blank 129.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level4{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:165.0000pt;
=20mso-level-number-position:left;
=20margin-left:165.0000pt;text-indent:-18.0000pt;tab-stops:blank 165.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level5{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:201.0000pt;
=20mso-level-number-position:left;
=20margin-left:201.0000pt;text-indent:-18.0000pt;tab-stops:blank 201.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level6{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:237.0000pt;
=20mso-level-number-position:left;
=20margin-left:237.0000pt;text-indent:-18.0000pt;tab-stops:blank 237.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level7{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:273.0000pt;
=20mso-level-number-position:left;
=20margin-left:273.0000pt;text-indent:-18.0000pt;tab-stops:blank 273.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level8{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:309.0000pt;
=20mso-level-number-position:left;
=20margin-left:309.0000pt;text-indent:-18.0000pt;tab-stops:blank 309.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l1:level9{
=20mso-level-number-format:bullet;
=20mso-level-suffix:tab;
=20mso-level-text:"";
=20mso-level-tab-stop:345.0000pt;
=20mso-level-number-position:left;
=20margin-left:345.0000pt;text-indent:-18.0000pt;tab-stops:blank 345.0000pt ;font-family:Wingdings;font-size:10.0000pt;}
=20
=20@list l2:level1{
=20mso-level-start-at:5;
=20mso-level-number-format:chinese-counting;
=20mso-level-suffix:none;
=20mso-level-text:"%1、";
=20mso-level-tab-stop:none;
=20mso-level-number-position:left;
=20margin-left:0.0000pt;
=20text-indent:0.0000pt;
=20}
=20
=20p.MsoNormal{
=20mso-style-name:正文;
=20mso-style-parent:"";
=20margin:0pt;
=20margin-bottom:.0001pt;
=20mso-pagination:none;
=20text-align:justify;
=20text-justify:inter-ideograph;
=20font-family:Calibri;
=20mso-fareast-font-family:宋体;
=20mso-bidi-font-family:'Times New Roman';
=20font-size:10.5000pt;
=20mso-font-kerning:1.0000pt;
=20}
=20
=20h3{
=20mso-style-name:"标题 3";
=20mso-style-noshow:yes;
=20mso-style-next:正文;
=20margin-top:5.0000pt;
=20margin-bottom:5.0000pt;
=20mso-margin-top-alt:auto;
=20mso-margin-bottom-alt:auto;
=20mso-pagination:none;
=20text-align:left;
=20font-family:宋体;
=20font-weight:bold;
=20font-size:13.5000pt;
=20}
=20
=20span.10{
=20font-family:'Times New Roman';
=20}
=20
=20span.15{
=20font-family:'Times New Roman';
=20mso-ansi-font-style:italic;
=20}
=20
=20span.16{
=20font-family:'Times New Roman';
=20mso-ansi-font-weight:bold;
=20}
=20
=20span.17{
=20font-family:'Times New Roman';
=20mso-ansi-font-weight:bold;
=20}
=20
=20span.18{
=20font-family:'Times New Roman';
=20}
=20
=20p.p{
=20mso-style-name:"普通\(网站\)";
=20margin-top:5.0000pt;
=20margin-right:0.0000pt;
=20margin-bottom:5.0000pt;
=20margin-left:0.0000pt;
=20mso-margin-top-alt:auto;
=20mso-margin-bottom-alt:auto;
=20mso-pagination:none;
=20text-align:left;
=20font-family:Calibri;
=20mso-fareast-font-family:宋体;
=20mso-bidi-font-family:'Times New Roman';
=20font-size:12.0000pt;
=20}
=20
=20span.msoIns{
=20mso-style-type:export-only;
=20mso-style-name:"";
=20text-decoration:underline;
=20text-underline:single;
=20color:blue;
=20}
=20
=20span.msoDel{
=20mso-style-type:export-only;
=20mso-style-name:"";
=20text-decoration:line-through;
=20color:red;
=20}
=20
=20table.MsoNormalTable{
=20mso-style-name:普通表格;
=20mso-style-parent:"";
=20mso-style-noshow:yes;
=20mso-tstyle-rowband-size:0;
=20mso-tstyle-colband-size:0;
=20mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
=20mso-para-margin:0pt;
=20mso-para-margin-bottom:.0001pt;
=20mso-pagination:widow-orphan;
=20font-family:'Times New Roman';
=20font-size:10.0000pt;
=20mso-ansi-language:#0400;
=20mso-fareast-language:#0400;
=20mso-bidi-language:#0400;
=20}
=20
=20table.MsoTableGrid{
=20mso-style-name:网格型;
=20mso-tstyle-rowband-size:0;
=20mso-tstyle-colband-size:0;
=20mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
=20mso-border-top-alt:0.5000pt solid windowtext;
=20mso-border-left-alt:0.5000pt solid windowtext;
=20mso-border-bottom-alt:0.5000pt solid windowtext;
=20mso-border-right-alt:0.5000pt solid windowtext;
=20mso-border-insideh:0.5000pt solid windowtext;
=20mso-border-insidev:0.5000pt solid windowtext;
=20mso-para-margin:0pt;
=20mso-para-margin-bottom:.0001pt;
=20mso-pagination:none;
=20text-align:justify;
=20text-justify:inter-ideograph;
=20font-family:'Times New Roman';
=20font-size:10.0000pt;
=20mso-ansi-language:#0400;
=20mso-fareast-language:#0400;
=20mso-bidi-language:#0400;
=20}
=20@page{mso-page-border-surround-header:no;
=20	mso-page-border-surround-footer:no;}@page Section0{
=20margin-top:72.0000pt;
=20margin-bottom:72.0000pt;
=20margin-left:90.0000pt;
=20margin-right:90.0000pt;
=20size:595.3000pt 841.9000pt;
=20layout-grid:15.6000pt;
=20mso-header-margin:42.5500pt;
=20mso-footer-margin:49.6000pt;
=20}
=20div.Section0{page:Section0;}</style></head><body style=3D"tab-interval:21pt;text-justify-trim:punctuation;" ><!--StartFragment--><div class=3D"Section0"  style=3D"layout-grid:15.6000pt;" ><h3 style=3D"text-indent:27.1000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" >GoFreight<font face=3D"宋体" >货运司机真实信息承诺协议</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><p class=3Dp  style=3D"text-indent:24.1000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >生效日期</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >：</font><font face=3D"Times New Roman" >202</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >5</span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >年</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >9</span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >月</font>1<font face=3D"宋体" >日</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><br></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >&nbsp;&nbsp;</span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >承诺方</font></span></b><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" >&nbsp;</span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >：注册司机（以下简称</font><font face=3D"Times New Roman" >“</font><font face=3D"宋体" >您</font><font face=3D"Times New Roman" >”</font><font face=3D"宋体" >）</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-left:0.0000pt;text-indent:21.0000pt;mso-char-indent-count:2.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><hr size=3D"2"  width=3D"100%"  align=3Dcenter ></p><h3 style=3D"text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >一、承诺范围</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><p class=3Dp  style=3D"margin-left:0.0000pt;text-indent:24.1000pt;mso-char-indent-count:2.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >您保证向</font>GoFreight<font face=3D"宋体" >平台提供的以下信息</font></span></b><b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >实时更新且真实有效</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >：</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:24.1000pt;
=20mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20mso-bidi-font-weight:normal;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >1.1</span></b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
=20mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >&nbsp;</span><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
=20mso-bidi-font-weight:bold;text-transform:none;font-style:normal;" >&nbsp;</span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >基础资质</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >驾驶证、营运许可证有效期（需符合哈国《道路运输法》第</font>48<font face=3D"宋体" >条）</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >司机身份信息（姓名、护照号、生物识别照片）</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:24.1000pt;
=20mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20mso-bidi-font-weight:normal;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >1.2</span></b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
=20mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >&nbsp;&nbsp;</span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >车辆信息</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >车牌号、车型、载重吨位、保险单号及有效期</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >车辆年检记录（需上传哈国交通部门盖章文件）</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:24.1000pt;
=20mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20mso-bidi-font-weight:normal;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >1.3 </span></b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
=20mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
=20font-size:12.0000pt;" >&nbsp;</span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >运输行为</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >运输途中实时定位及轨迹记录</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >货物签收确认凭证（收件人签字</font>/<font face=3D"宋体" >电子验签）</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-left:0.0000pt;text-indent:21.0000pt;mso-char-indent-count:2.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><hr size=3D"2"  width=3D"100%"  align=3Dcenter ></p><h3 style=3D"text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >二、司机专属责任</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><p class=3Dp  style=3D"text-indent:24.1000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;" ><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" >2.1 </span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" >&nbsp;</span></b><b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:10.5000pt;
=20mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >动态更新义务</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >驾驶证</font>/<font face=3D"宋体" >保险到期前</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" >30<font face=3D"宋体" >天</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >需更新平台信息；</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >车辆故障或更换需在</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >发车前</font>24<font face=3D"宋体" >小时</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >报备。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:24.1000pt;
=20mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:1.0000pt;" >2.2</span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:1.0000pt;" >&nbsp;&nbsp;&nbsp;</span></b><b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >违规后果</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-weight:bold;font-size:12.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></b></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >使用无效证件接单：扣除当笔运费并暂停服务资格</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" >90<font face=3D"宋体" >天</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >；</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;text-transform:none;font-style:normal;
=20font-size:10.0000pt;mso-font-kerning:1.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >轨迹造假（如关闭</font>GPS<font face=3D"宋体" >）：承担全部货损责任及平台追偿金（不低于货物价</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >值</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:63.0000pt;
=20mso-char-indent-count:6.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" >20%<font face=3D"宋体" >）。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-left:0.0000pt;text-indent:21.0000pt;mso-char-indent-count:2.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><hr size=3D"2"  width=3D"100%"  align=3Dcenter ></p><h3 style=3D"text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >三、平台免责声明</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><table class=3DMsoTableGrid  border=3D1  cellspacing=3D0  style=3D"border-collapse:collapse;width:427.1000pt;margin-left:2.0000pt;
=20border:none;mso-border-left-alt:0.5000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;
=20mso-border-right-alt:0.5000pt solid windowtext;mso-border-bottom-alt:0.5000pt solid windowtext;mso-border-insideh:0.5000pt solid windowtext;
=20mso-border-insidev:0.5000pt solid windowtext;mso-padding-alt:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;" ><tr><td width=3D569  valign=3Dtop  colspan=3D2  style=3D"width:427.1000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:1.5000pt solid rgb(0,0,0);mso-border-top-alt:1.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  style=3D"text-indent:24.1000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;text-align:left;" ><b><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;font-weight:bold;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >3.1 </span></b><b><span class=3D"16"  style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;font-weight:bold;text-transform:none;
=20font-style:normal;" ><font face=3D"宋体" >司机信息错误后果</font></span></b><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:normal;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr><td width=3D146  valign=3Dtop  style=3D"width:109.5000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"layout-grid-mode:char;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;
=20text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >提供过期驾驶证</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td><td width=3D423  valign=3Dtop  style=3D"width:317.6000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:none;
=20mso-border-left-alt:none;border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:1.0000pt solid rgb(0,0,0);mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"text-indent:21.0000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >交警处罚、车辆扣留等损失由司机全额承担</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr><td width=3D146  valign=3Dtop  style=3D"width:109.5000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"layout-grid-mode:char;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;
=20text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >车辆载重超限</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td><td width=3D423  valign=3Dtop  style=3D"width:317.6000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:none;
=20mso-border-left-alt:none;border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"text-indent:21.0000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >道路损坏赔偿及罚金由司机自行支付（依据哈国《公路法》第</font>112<font face=3D"宋体" >条）</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr><td width=3D146  valign=3Dtop  style=3D"width:109.5000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"layout-grid-mode:char;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;
=20text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >擅自变更运输路线</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td><td width=3D423  valign=3Dtop  style=3D"width:317.6000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:none;
=20mso-border-left-alt:none;border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"text-indent:21.0000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >导致海关监管违规的滞纳金由司机承担</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr><td width=3D146  valign=3Dtop  style=3D"width:109.5000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"layout-grid-mode:char;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;
=20text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >未核实收件人身份</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td><td width=3D423  valign=3Dtop  style=3D"width:317.6000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:none;
=20mso-border-left-alt:none;border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  align=3Dcenter  style=3D"text-indent:21.0000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;text-align:center;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >货物误交造成的损失从司机保证金扣除</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr><td width=3D569  valign=3Dtop  colspan=3D2  style=3D"width:427.1000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.0000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:0.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  style=3D"text-indent:24.1000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >3.2 </span></b><b><span class=3D"17"  style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >例外情形</font></span></b><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:normal;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr><tr style=3D"height:24.8000pt;" ><td width=3D569  valign=3Dtop  colspan=3D2  style=3D"width:427.1000pt;padding:3.2000pt 6.4000pt 3.2000pt 6.4000pt ;border-left:1.5000pt solid rgb(0,0,0);
=20mso-border-left-alt:1.5000pt solid rgb(0,0,0);border-right:1.0000pt solid rgb(0,0,0);mso-border-right-alt:0.5000pt solid rgb(0,0,0);
=20border-top:none;mso-border-top-alt:0.5000pt solid rgb(0,0,0);border-bottom:1.5000pt solid rgb(0,0,0);
=20mso-border-bottom-alt:1.5000pt solid rgb(0,0,0);background:rgb(255,255,255);" ><p class=3Dp  style=3D"text-indent:21.0000pt;mso-char-indent-count:2.0000;layout-grid-mode:char;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >平台仅在</font></span><span class=3D"17"  style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >系统主动推送错误信息</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20mso-ansi-font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >（如导航路线规划错误）导致损失时承担相应责任。</font></span><span style=3D"font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:normal;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></td></tr></table><h3 style=3D"text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >四、违约处理机制</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><p class=3Dp  style=3D"text-indent:24.0000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=3Dp  style=3D"text-indent:24.0000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" >4.1 </span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >信用分级处罚</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >：</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:71.4500pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.1500pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l1 level1 lfo2;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
=20font-style:normal;font-size:10.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >初级违规</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >（信息未更新）：冻结账户直至信息更新完成；</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
=20font-style:normal;font-size:10.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >中级违规</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >（轨迹异常</font><font face=3D"Times New Roman" >/</font><font face=3D"宋体" >超速）：扣减当月运费</font><font face=3D"Times New Roman" >10% + </font><font face=3D"宋体" >强制安全培训；</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:72.0000pt;
=20mso-para-margin-left:0.0000gd;text-indent:-18.0000pt;mso-char-indent-count:0.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l0 level2 lfo1;" ><![if !supportLists]><span style=3D"font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
=20letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
=20font-style:normal;font-size:10.0000pt;" ><span style=3D'mso-list:Ignore;' >&#183;<span>&nbsp;</span></span></span><![endif]><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >严重违规</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >（证件造假</font><font face=3D"Times New Roman" >/</font><font face=3D"宋体" >走私）：永久封号并上报哈国</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >交通运输失信名单</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:24.0000pt;
=20mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;" >4.2 </span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;" ><font face=3D"宋体" >优先追偿权</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >：</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><br></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >因司机信息不实导致平台被索赔的，平台有权从</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >运费、保证金、押金中直接扣款</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-left:0.0000pt;text-indent:21.0000pt;mso-char-indent-count:2.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><hr size=3D"2"  width=3D"100%"  align=3Dcenter ></p><h3 style=3D"text-autospace:ideograph-numeric;mso-pagination:widow-orphan;mso-list:l2 level1 lfo3;" ><![if !supportLists]><span style=3D"font-family:宋体;mso-ascii-font-family:'Times New Roman';mso-hansi-font-family:'Times New Roman';
=20mso-bidi-font-family:'Times New Roman';color:rgb(0,0,0);letter-spacing:0.0000pt;
=20mso-ansi-font-weight:bold;text-transform:none;font-style:normal;" ><span style=3D'mso-list:Ignore;' >五、</span></span><![endif]><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >生效与确认</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:13.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><h3 style=3D"margin-left:36.0000pt;mso-para-margin-left:2.2800gd;text-indent:-12.0500pt;
=20mso-char-indent-count:-1.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;
=20mso-outline-level:3;" ><b><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" >5.1<font face=3D"宋体" >司机电子签署声明：</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" ><br></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:normal;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >“</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:normal;
=20text-transform:none;font-style:normal;font-size:10.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >本人保证所提交的证件、车辆、行为信息真实有效，已知晓虚假信息将承担法律责任。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:normal;
=20text-transform:none;font-style:normal;font-size:10.5000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></h3><p class=3Dp  style=3D"margin-right:36.0000pt;text-indent:31.5000pt;mso-char-indent-count:3.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:normal;
=20text-transform:none;font-style:normal;font-size:10.5000pt;" ><font face=3D"宋体" >同意平台调用行车数据</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:normal;
=20text-transform:none;font-style:normal;font-size:10.5000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >用于运输安全分析（含时速、急刹车等）。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-weight:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><h3 style=3D"text-indent:24.1000pt;mso-char-indent-count:2.0000;text-autospace:ideograph-numeric;
=20mso-pagination:widow-orphan;mso-outline-level:3;" ><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;" >5.2</span></b><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;" ><font face=3D"宋体" >重点责任切割条款</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font></span></b><b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
=20text-transform:none;font-style:normal;font-size:12.0000pt;
=20mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></h3><p class=3Dp  style=3D"margin-left:47.9000pt;mso-para-margin-left:4.5600gd;text-indent:0.0000pt;
=20mso-char-indent-count:0.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#10071;</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;" ><font face=3D"宋体" >行车数据归属</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><font face=3D"Times New Roman" >&#8203;</font><font face=3D"宋体" >：</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><br></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face=3D"宋体" >您理解并同意：</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;margin-left:52.5000pt;
=20mso-para-margin-left:4.0000gd;text-indent:-10.5000pt;mso-char-indent-count:-1.0000;
=20text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >（</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" >1</span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >）、</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >运输过程中产生的</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;font-size:10.5000pt;" ><font face=3D"宋体" >车速、油耗、急转弯次数</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >等数据由平台记录，并作为纠纷判定依据；</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=3DMsoNormal  style=3D"margin-top:0.0000pt;margin-bottom:0.0000pt;text-indent:42.0000pt;
=20mso-char-indent-count:4.0000;text-autospace:ideograph-numeric;mso-pagination:widow-orphan;" ><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >（</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" >2</span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >）、</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >因您</font></span><b style=3D"mso-bidi-font-weight:normal" ><span class=3D"16"  style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
=20text-transform:none;font-style:normal;font-size:10.5000pt;" ><font face=3D"宋体" >主动关闭车载设备、屏蔽信号</font></span></b><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
=20font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><font face=3D"宋体" >导致数据缺失的，视为您放弃抗辩权。</font></span><span style=3D"mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
=20font-size:10.5000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p></div><!--EndFragment--></body></html>

------=_NextPart_000_0076_01C29953.BE473C30
Content-Type: application/octet-stream;
Content-Transfer-Encoding: base64
Content-Location: file:///C:/Users/<USER>/AppData/Local/Temp/~tmp{7ce5ea00-38ef-48df-be93-ac39d18253c6}1509938.files/filelist.xml

PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/Pg0K
PHhtbCB4bWxuczpvPSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOm9mZmljZTpvZmZpY2UiPjxv
Ok1haW5GaWxlIEhSZWY9Ii4uL350bXB7N2NlNWVhMDAtMzhlZi00OGRmLWJlOTMtYWMzOWQxODI1
M2M2fTE1MDk5MzgiLz48bzpGaWxlIEhSZWY9ImZpbGVsaXN0LnhtbCIvPjwveG1sPg==

------=_NextPart_000_0076_01C29953.BE473C30--