<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
	xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta name="ProgId" content="Word.Document">
	<meta name="Generator" content="Microsoft Word 14">
	<meta name="Originator" content="Microsoft Word 14">
	<title></title>
	<!--[if gte mso 9]><xml><o:DocumentProperties><o:Author>Lycon</o:Author><o:LastAuthor>觞.无痕</o:LastAuthor><o:Revision>1</o:Revision><o:Pages>3</o:Pages><o:Characters>1295</o:Characters></o:DocumentProperties><o:CustomDocumentProperties><o:KSOProductBuildVer dt:dt="string" >2052-12.1.0.22529</o:KSOProductBuildVer><o:ICV dt:dt="string" >F96E9001C6804C5EB60AA2221F974C81_13</o:ICV><o:KSOTemplateDocerSaveRecord dt:dt="string" >eyJoZGlkIjoiNzQ3YzU1NjQ4NzMzMjg2YTE1OWEyNmVlZGY0ZGM2OTEiLCJ1c2VySWQiOiIxMDY3ODAxMTczIn0=</o:KSOTemplateDocerSaveRecord></o:CustomDocumentProperties></xml><![endif]--><!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]--><!--[if gte mso 9]><xml><w:WordDocument><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery><w:DocumentKind>DocumentNotSpecified</w:DocumentKind><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing><w:View>Web</w:View><w:Compatibility><w:AdjustLineHeightInTable/><w:DontGrowAutofit/><w:BalanceSingleByteDoubleByteWidth/><w:DoNotExpandShiftReturn/><w:UseFELayout/></w:Compatibility><w:Zoom>0</w:Zoom></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml><w:LatentStyles DefLockedState="false"  DefUnhideWhenUsed="true"  DefSemiHidden="true"  DefQFormat="false"  DefPriority="99"  LatentStyleCount="260" >
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 7" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 8" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 9" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 7" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 8" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 9" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 7" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 8" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 9" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Normal Indent" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="header" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footer" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index heading" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="caption" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of figures" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope address" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope return" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote reference" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation reference" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="line number" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="page number" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote reference" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of authorities" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="macro" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toa heading" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Title" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Closing" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Signature" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Default Paragraph Font" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Message Header" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Subtitle" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Salutation" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Date" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Note Heading" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Block Text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Hyperlink" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="FollowedHyperlink" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Strong" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Emphasis" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Document Map" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Plain Text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="E-mail Signature" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal (Web)" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Acronym" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Address" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Cite" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Code" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Definition" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Keyboard" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Preformatted" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Sample" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Typewriter" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Variable" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal Table" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation subject" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No List" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / a / i" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / 1.1 / 1.1.1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Article / Section" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 7" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 8" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 7" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 8" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Contemporary" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Elegant" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Professional" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Balloon Text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Theme" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Placeholder Text" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No Spacing" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Paragraph" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Quote" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Intense Quote" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 1" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 2" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 3" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 4" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 5" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 6" ></w:LsdException>
 <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 6" ></w:LsdException>
 </w:LatentStyles></xml><![endif]-->
	<style>
		@font-face {
			font-family: "Times New Roman";
		}

		@font-face {
			font-family: "宋体";
		}

		@font-face {
			font-family: "Wingdings";
		}

		@font-face {
			font-family: "Symbol";
		}

		@font-face {
			font-family: "Courier New";
		}

		@font-face {
			font-family: "Calibri";
		}

		@list l0:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l0:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l0:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l0:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l1:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l1:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l1:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l2:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l2:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l2:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l3:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l3:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l3:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l4:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l4:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l4:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level1 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 36.0000pt;
			mso-level-number-position: left;
			margin-left: 36.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 36.0000pt;
			font-family: Symbol;
			font-size: 10.0000pt;
		}

		@list l5:level2 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "o";
			mso-level-tab-stop: 72.0000pt;
			mso-level-number-position: left;
			margin-left: 72.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 72.0000pt;
			font-family: 'Courier New';
			font-size: 10.0000pt;
		}

		@list l5:level3 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 108.0000pt;
			mso-level-number-position: left;
			margin-left: 108.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 108.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level4 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 144.0000pt;
			mso-level-number-position: left;
			margin-left: 144.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 144.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level5 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 180.0000pt;
			mso-level-number-position: left;
			margin-left: 180.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 180.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level6 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 216.0000pt;
			mso-level-number-position: left;
			margin-left: 216.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 216.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level7 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 252.0000pt;
			mso-level-number-position: left;
			margin-left: 252.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 252.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level8 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 288.0000pt;
			mso-level-number-position: left;
			margin-left: 288.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 288.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		@list l5:level9 {
			mso-level-number-format: bullet;
			mso-level-suffix: tab;
			mso-level-text: "";
			mso-level-tab-stop: 324.0000pt;
			mso-level-number-position: left;
			margin-left: 324.0000pt;
			text-indent: -18.0000pt;
			tab-stops: blank 324.0000pt;
			font-family: Wingdings;
			font-size: 10.0000pt;
		}

		p.MsoNormal {
			mso-style-name: 正文;
			mso-style-parent: "";
			margin: 0pt;
			margin-bottom: .0001pt;
			mso-pagination: none;
			text-align: justify;
			text-justify: inter-ideograph;
			font-family: Calibri;
			mso-fareast-font-family: 宋体;
			mso-bidi-font-family: 'Times New Roman';
			font-size: 10.5000pt;
			mso-font-kerning: 1.0000pt;
		}

		h3 {
			mso-style-name: "标题 3";
			mso-style-noshow: yes;
			mso-style-next: 正文;
			margin-top: 5.0000pt;
			margin-bottom: 5.0000pt;
			mso-margin-top-alt: auto;
			mso-margin-bottom-alt: auto;
			mso-pagination: none;
			text-align: left;
			font-family: 宋体;
			font-weight: bold;
			font-size: 13.5000pt;
		}

		h4 {
			mso-style-name: "标题 4";
			mso-style-noshow: yes;
			mso-style-next: 正文;
			margin-top: 5.0000pt;
			margin-bottom: 5.0000pt;
			mso-margin-top-alt: auto;
			mso-margin-bottom-alt: auto;
			mso-pagination: none;
			text-align: left;
			font-family: 宋体;
			font-weight: bold;
			font-size: 12.0000pt;
		}

		span.10 {
			font-family: 'Times New Roman';
		}

		span.15 {
			font-family: 'Times New Roman';
			mso-ansi-font-weight: bold;
		}

		span.16 {
			font-family: 'Times New Roman';
			mso-ansi-font-style: italic;
		}

		p.p {
			mso-style-name: "普通\(网站\)";
			margin-top: 5.0000pt;
			margin-right: 0.0000pt;
			margin-bottom: 5.0000pt;
			margin-left: 0.0000pt;
			mso-margin-top-alt: auto;
			mso-margin-bottom-alt: auto;
			mso-pagination: none;
			text-align: left;
			font-family: Calibri;
			mso-fareast-font-family: 宋体;
			mso-bidi-font-family: 'Times New Roman';
			font-size: 12.0000pt;
		}

		span.msoIns {
			mso-style-type: export-only;
			mso-style-name: "";
			text-decoration: underline;
			text-underline: single;
			color: blue;
		}

		span.msoDel {
			mso-style-type: export-only;
			mso-style-name: "";
			text-decoration: line-through;
			color: red;
		}

		table.MsoNormalTable {
			mso-style-name: 普通表格;
			mso-style-parent: "";
			mso-style-noshow: yes;
			mso-tstyle-rowband-size: 0;
			mso-tstyle-colband-size: 0;
			mso-padding-alt: 0.0000pt 5.4000pt 0.0000pt 5.4000pt;
			mso-para-margin: 0pt;
			mso-para-margin-bottom: .0001pt;
			mso-pagination: widow-orphan;
			font-family: 'Times New Roman';
			font-size: 10.0000pt;
			mso-ansi-language: #0400;
			mso-fareast-language: #0400;
			mso-bidi-language: #0400;
		}

		table.MsoTableGrid {
			mso-style-name: 网格型;
			mso-tstyle-rowband-size: 0;
			mso-tstyle-colband-size: 0;
			mso-padding-alt: 0.0000pt 5.4000pt 0.0000pt 5.4000pt;
			mso-border-top-alt: 0.5000pt solid windowtext;
			mso-border-left-alt: 0.5000pt solid windowtext;
			mso-border-bottom-alt: 0.5000pt solid windowtext;
			mso-border-right-alt: 0.5000pt solid windowtext;
			mso-border-insideh: 0.5000pt solid windowtext;
			mso-border-insidev: 0.5000pt solid windowtext;
			mso-para-margin: 0pt;
			mso-para-margin-bottom: .0001pt;
			mso-pagination: none;
			text-align: justify;
			text-justify: inter-ideograph;
			font-family: 'Times New Roman';
			font-size: 10.0000pt;
			mso-ansi-language: #0400;
			mso-fareast-language: #0400;
			mso-bidi-language: #0400;
		}

		@page {
			mso-page-border-surround-header: no;
			mso-page-border-surround-footer: no;
		}

		@page Section0 {
			margin-top: 72.0000pt;
			margin-bottom: 72.0000pt;
			margin-left: 90.0000pt;
			margin-right: 90.0000pt;
			size: 595.3000pt 841.9000pt;
			layout-grid: 15.6000pt;
			mso-header-margin: 42.5500pt;
			mso-footer-margin: 49.6000pt;
		}

		div.Section0 {
			page: Section0;
		}
	</style>
</head>

<body style="tab-interval:21pt;text-justify-trim:punctuation;"><!--StartFragment-->
	<div class="Section0" style="layout-grid:15.6000pt;">
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><span class="10"
				style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;">GoFreight<font face="宋体">
					货运司机隐私政策</font></span><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">生效日期</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
				<font face="Times New Roman">202</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">5</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">年</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">9</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">月</font>1<font face="宋体">日</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br></span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">适用对象</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：注册于</font>
				<font face="Times New Roman">GoFreight</font>
				<font face="宋体">平台的货运司机</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">一、司机个人信息收集范围</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">我们仅收集与运输服务直接相关的必要信息：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<table class="MsoTableGrid" border="1" cellspacing="0" style="border-collapse:collapse;width:446.8500pt;margin-left:-1.0000pt;
 border:none;mso-border-left-alt:0.5000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;
 mso-border-right-alt:0.5000pt solid windowtext;mso-border-bottom-alt:0.5000pt solid windowtext;mso-border-insideh:0.5000pt solid windowtext;
 mso-border-insidev:0.5000pt solid windowtext;mso-padding-alt:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;">
			<tbody>
				<tr>
					<td width="89" valign="center" style="width:66.9500pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<b><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">信息类别</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="222" valign="center" style="width:167.1000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<b><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">具体内容</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="283" valign="center" style="width:212.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<b><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">使用目的</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
				</tr>
				<tr>
					<td width="89" valign="center" style="width:66.9500pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">身份认证</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="222" valign="center" style="width:167.1000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<font face="宋体">姓名、护照号、驾驶证扫描件</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:normal;mso-bidi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="283" valign="center" style="width:212.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<font face="宋体">验证司机从业资质（哈国《道路运输条例》第</font>21<font face="宋体">条）</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:normal;mso-bidi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
				<tr>
					<td width="89" valign="center" style="width:66.9500pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">车辆数据</font>
								</span></b><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="222" valign="center" style="width:167.1000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">车牌号、</font>GPS<font face="宋体">轨迹、车速</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="283" valign="center" style="width:212.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">运输安全监控、路线优化、费用结算</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
				<tr>
					<td width="89" valign="center" style="width:66.9500pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">行为信息</font>
								</span></b><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="222" valign="center" style="width:167.1000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">接单记录、装卸货时间戳、收件人电子签名</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="283" valign="center" style="width:212.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">服务质控、纠纷取证（哈国《电子商务证据法》第</font>5<font face="宋体">条）</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
			</tbody>
		</table>
		<p class="p" style="margin-right:36.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">生物信息特别声明</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br></span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">人脸数据仅用于</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">首次注册实名认证</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">及</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">高风险订单二次验证</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">，</font>
				<font face="Times New Roman">​</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">不存储原始生物特征</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">（采用哈希值脱敏技术）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">二、敏感数据处理规则</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">2.1 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">行车轨迹数据</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l1 level1 lfo1;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">持续收集：运输任务中强制开启</font>GPS<font face="宋体">定位（哈国《货运安全法》要求）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l1 level1 lfo1;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">脱敏处理：行程结束后</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">72<font face="宋体">小时</font></span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">内删除精确坐标（仅保留行政区级路径）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">2.2 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">生物识别数据</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l1 level1 lfo1;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">采集授权：每次人脸验证需单独弹窗确认</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l1 level1 lfo1;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">存储期限：验证完成后</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">立即删除</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">原始图像（仅留加密特征值）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">三、数据共享与跨境传输</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">3.1 </span></b><b><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">境内共享</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></p>
		<table class="MsoTableGrid" border="1" cellspacing="0" style="border-collapse:collapse;width:426.1000pt;margin-left:-1.0000pt;
 border:none;mso-border-left-alt:0.5000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;
 mso-border-right-alt:0.5000pt solid windowtext;mso-border-bottom-alt:0.5000pt solid windowtext;mso-border-insideh:0.5000pt solid windowtext;
 mso-border-insidev:0.5000pt solid windowtext;mso-padding-alt:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;">
			<tbody>
				<tr>
					<td width="131" valign="center" style="width:98.6000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">接收方</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="140" valign="center" style="width:105.7000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">共享内容</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="295" valign="center" style="width:221.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:1.5000pt solid windowtext;mso-border-top-alt:1.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;"><b
								style="mso-bidi-font-weight:normal"><span class="15" style="font-family:'Times New Roman';mso-fareast-font-family:宋体;mso-ansi-font-weight:bold;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<font face="宋体">法律依据</font>
								</span></b><b style="mso-bidi-font-weight:normal"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
				</tr>
				<tr>
					<td width="131" valign="center" style="width:98.6000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">哈萨克斯坦交通部</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="140" valign="center" style="width:105.7000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">车牌号、驾驶证状态</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="295" valign="center" style="width:221.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">履行《商业运输监管义务》（第</font>411<font face="宋体">号政府令）</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
				<tr>
					<td width="131" valign="center" style="width:98.6000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.5000pt solid windowtext;
 mso-border-left-alt:1.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">合作加油站</font>/<font face="宋体">维修厂</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="140" valign="center" style="width:105.7000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">车辆型号、里程数</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="295" valign="center" style="width:221.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:none;
 mso-border-left-alt:none;border-right:1.5000pt solid windowtext;mso-border-right-alt:1.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.5000pt solid windowtext;
 mso-border-bottom-alt:1.5000pt solid windowtext;">
						<p class="p" align="center" style="layout-grid-mode:char;mso-pagination:widow-orphan;text-align:center;">
							<span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;font-size:10.5000pt;
 mso-font-kerning:0.0000pt;">
								<font face="宋体">司机专属折扣计划（需您单独授权）</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
			</tbody>
		</table>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">3.</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">2<font face="宋体">跨境运输</font></span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l2 level1 lfo2;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">向中国境内服务器传输</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">脱敏行车数据</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">（如平均时速、油耗统计）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l2 level1 lfo2;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">涉及个人身份的信息（如姓名、护照号）</font>
				<font face="Times New Roman">​</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">永久存储于哈国本地数据中心</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">四、司机数据权利</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:11.9500pt;mso-para-margin-left:1.1400gd;text-indent:0.0000pt;
 mso-char-indent-count:0.0000;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">您可通过</font>APP<font face="宋体">「司机端</font>
				<font face="Times New Roman">-</font>
				<font face="宋体">隐私中心」行使以下权利：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br></span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">4.1 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">数据查看权</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l5 level1 lfo3;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">实时查询被收集的行车数据（如昨日急刹车次数）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;text-indent:12.0000pt;mso-char-indent-count:1.0000;
 mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">4</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">.2 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">有限拒绝权</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l5 level1 lfo3;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">可关闭</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">非安全类数据</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">收集（如油耗统计用于市场分析）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l5 level1 lfo3;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;mso-ansi-font-weight:bold;text-transform:none;
 font-style:normal;font-size:10.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">不可关闭</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">安全类数据收集（</font>GPS<font face="宋体">定位、车速）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:46.5000pt;
 mso-para-margin-left:1.0000gd;mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;
 text-indent:-36.0000pt;mso-char-indent-count:-3.0000;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">4.3 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">删除权</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<table class="MsoTableGrid" border="1" cellspacing="0" style="border-collapse:collapse;width:426.1000pt;margin-left:-1.0000pt;
 border:none;mso-border-left-alt:0.5000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;
 mso-border-right-alt:0.5000pt solid windowtext;mso-border-bottom-alt:0.5000pt solid windowtext;mso-border-insideh:0.5000pt solid windowtext;
 mso-border-insidev:0.5000pt solid windowtext;mso-padding-alt:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;">
			<tbody>
				<tr>
					<td width="218" valign="center" style="width:163.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.0000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><b><span class="15" style="font-family:宋体;mso-ascii-font-family:'Times New Roman';mso-hansi-font-family:'Times New Roman';
 mso-bidi-font-family:'Times New Roman';color:rgb(0,0,0);letter-spacing:0.0000pt;
 font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
									<font face="宋体">数据类型</font>
								</span></b><b><span style="font-family:宋体;mso-ascii-font-family:'Times New Roman';mso-hansi-font-family:'Times New Roman';
 mso-bidi-font-family:'Times New Roman';color:rgb(0,0,0);letter-spacing:0.0000pt;
 font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
					<td width="349" valign="center" style="width:262.3000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:1.0000pt solid windowtext;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><b><span class="15" style="font-family:宋体;mso-ascii-font-family:'Times New Roman';mso-hansi-font-family:'Times New Roman';
 mso-bidi-font-family:'Times New Roman';color:rgb(0,0,0);letter-spacing:0.0000pt;
 font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
									<font face="宋体">删除条件</font>
								</span></b><b><span style="font-family:宋体;mso-ascii-font-family:'Times New Roman';mso-hansi-font-family:'Times New Roman';
 mso-bidi-font-family:'Times New Roman';color:rgb(0,0,0);letter-spacing:0.0000pt;
 font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
									<o:p></o:p>
								</span></b></p>
					</td>
				</tr>
				<tr>
					<td width="218" valign="center" style="width:163.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">人脸识别原始图像</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="349" valign="center" style="width:262.3000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">认证完成后即时触发删除</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
				<tr>
					<td width="218" valign="center" style="width:163.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">历史运输轨迹</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="349" valign="center" style="width:262.3000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">订单结束</font>72<font face="宋体">小时后自动删除</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
				<tr>
					<td width="218" valign="center" style="width:163.8000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">驾驶证扫描件</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
					<td width="349" valign="center" style="width:262.3000pt;padding:4.0000pt 6.4000pt 4.0000pt 6.4000pt ;border-left:1.0000pt solid windowtext;
 mso-border-left-alt:0.5000pt solid windowtext;border-right:1.0000pt solid windowtext;mso-border-right-alt:0.5000pt solid windowtext;
 border-top:none;mso-border-top-alt:0.5000pt solid windowtext;border-bottom:1.0000pt solid windowtext;
 mso-border-bottom-alt:0.5000pt solid windowtext;">
						<p class="MsoNormal" align="center" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;layout-grid-mode:char;mso-pagination:widow-orphan;
 text-align:center;"><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<font face="宋体">账户注销后</font>15<font face="宋体">个工作日内删除</font>
							</span><span style="font-family:'Times New Roman';mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
								<o:p></o:p>
							</span></p>
					</td>
				</tr>
			</tbody>
		</table>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p>&nbsp;</o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">五、安全保护措施</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">5.1 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">车载终端加密</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l4 level1 lfo4;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">OBD<font face="宋体">设备采用</font></span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">国密</font>SM4<font face="宋体">算法</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">加密传输数据（符合哈国《密码法》标准）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">5.2 </span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">生物信息保护</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l4 level1 lfo4;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">人脸识别通过</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">活体检测</font>+<font face="宋体">动态加密</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">实现（杜绝照片</font>/<font face="宋体">视频破解）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">5.3</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:1.0000pt;">&nbsp;</span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">事故响应机制</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l4 level1 lfo4;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">发生数据泄露后</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">24<font face="宋体">小时内</font></span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">向哈国数据保护局及受影响司机双向通报</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">六、儿童信息保护</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="Times New Roman">​</font></span><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">绝对禁止条款</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l0 level1 lfo5;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">未满</font>
			</span><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">23<font face="宋体">周岁</font></span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">人员禁止注册货运司机（哈国《职业运输驾驶员最低年龄法令》）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l0 level1 lfo5;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">系统强制拦截年龄不符的身份证件信息</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<h3 style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">七、政策更新</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<font face="Times New Roman">​</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:13.5000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></h3>
		<p class="p" style="text-indent:24.1000pt;mso-char-indent-count:2.0000;mso-pagination:widow-orphan;"><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">
					<font face="宋体">重大变更（如新增指纹收集）将通过：</font>
				</span></b><b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;
 mso-font-kerning:0.0000pt;">
					<o:p></o:p>
				</span></b></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;mso-margin-top-alt:auto;
 mso-margin-bottom-alt:auto;text-indent:21.0000pt;mso-char-indent-count:2.0000;
 mso-pagination:widow-orphan;"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
				<font face="宋体">车载终端语音播报</font>
			</span><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
				<font face="宋体">、</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">司机账号绑定的</font>
			</span><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
				<font face="宋体">哈萨克斯坦手机号短信通知</font>
			</span><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
				<font face="宋体">、</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">线下合作车队</font>
			</span><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;">
				<font face="宋体">书面公告</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:normal;
 mso-bidi-font-weight:bold;text-transform:none;font-style:normal;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="Times New Roman">​</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 mso-ansi-font-weight:normal;mso-bidi-font-weight:bold;font-size:10.5000pt;
 mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"></p>
		<hr size="2" width="100%" align="center">
		<p></p>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">八、</font>
				</span></b><b style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;">
					<font face="宋体">司机专属申诉渠道</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="Times New Roman">​</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l3 level1 lfo6;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">隐私负责人：</font><EMAIL>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l3 level1 lfo6;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">监管机构投诉：哈萨克斯坦运输业数据保护办公室（努尔苏丹市</font>Dostyk St 9<font face="宋体">）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="MsoNormal" style="margin-top:5.0000pt;margin-bottom:5.0000pt;margin-left:36.0000pt;
 mso-margin-top-alt:auto;mso-margin-bottom-alt:auto;text-indent:-18.0000pt;
 tab-stops:left blank 36.0000pt ;mso-pagination:widow-orphan;mso-list:l3 level1 lfo6;"><!--[if !supportLists]--><span
				style="font-family:Symbol;mso-fareast-font-family:宋体;color:rgb(0,0,0);
 letter-spacing:0.0000pt;text-transform:none;font-style:normal;
 font-size:10.0000pt;mso-font-kerning:1.0000pt;"><span
					style="mso-list:Ignore;">·<span>&nbsp;</span></span></span><!--[endif]--><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<font face="宋体">紧急处置电话：</font>+7 7172 123 456<font face="宋体">（行车数据泄露时启用）</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:1.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="p" style="margin-right:36.0000pt;margin-left:36.0000pt;mso-pagination:widow-orphan;"><b
				style="mso-bidi-font-weight:normal"><span class="15" style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;mso-ansi-font-weight:bold;
 text-transform:none;font-style:normal;font-size:12.0000pt;">
					<font face="宋体">重点提示</font>
				</span></b><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">：</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="p" style="margin-right:36.0000pt;margin-left:36.0000pt;text-indent:21.0000pt;
 mso-char-indent-count:2.0000;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">关闭</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">
				<font face="宋体">手机</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 color:rgb(0,0,0);letter-spacing:0.0000pt;text-transform:none;
 font-style:normal;font-size:10.5000pt;mso-font-kerning:0.0000pt;">GPS<font face="宋体">定位功能即视为自动放弃运输资格，由此产生的罚款</font>
				<font face="Times New Roman">/</font>
				<font face="宋体">解约责任由司机承担</font>
			</span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:宋体;
 font-size:10.5000pt;mso-font-kerning:0.0000pt;">
				<o:p></o:p>
			</span></p>
		<p class="p" style="margin-left:0.0000pt;text-indent:0.0000pt;mso-pagination:widow-orphan;"><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:宋体;
 mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;">
				<o:p>&nbsp;</o:p>
			</span></p>
	</div><!--EndFragment-->

</body>

</html>