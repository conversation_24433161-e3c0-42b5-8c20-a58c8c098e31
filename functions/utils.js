const { Timestamp } = require("firebase-admin/firestore");
const { getMessaging } = require("firebase-admin/messaging");
const { getRemoteConfig } = require("firebase-admin/remote-config");
const path = require('path');
const { getStorage } = require('firebase-admin/storage');

function bodyFromBasicAuthRequest(req, res, auth, httpMethod = 'POST', props = []) {
  if (req.headers.authorization !== "Basic " + auth) {
    res.status(401).send("Unauthorized");
    return;
  }
  if (req.method !== httpMethod) {
    res.status(405).send("Method Not Allowed");
    return;
  }
  const { body } = req;
  if (typeof body === "string") {
    body = JSON.parse(body);
  }
  for (const prop of props) {
    if (body[prop] == null) {
      res.status(400).send("Bad Request");
      return;
    }
  }

  return body;
}

async function syncDriver(driver, ref, larkClient) {
  const { fields, record_id } = driver;
  const nameEn = fields['司机姓名(英文)'] ? fields['司机姓名(英文)'][0].text : ''
  const nameRu = fields['司机姓名(俄文)'] ? fields['司机姓名(俄文)'][0].text : ''
  const id = fields['司机编号']
  const licenseImgToken = fields['驾照'] ? fields['驾照'][0].file_token : ''
  const passportImgToken = fields['护照'] ? fields['护照'][0].file_token : ''
  const foreignPhone = fields['外国电话'] ? fields['外国电话'][0].text : ''
  const foreignPhoneOthers = fields['外国电话(其他)'] ? fields['外国电话(其他)'][0].text : ''
  const chinesePhone = fields['中国电话'] ? fields['中国电话'][0].text : ''
  const useWhatsapp = fields['是否用 whatsapp'] ?? ''
  const wechat = fields['微信号'] ? fields['微信号'][0].text : ''
  let phones = []
  if (foreignPhone.length > 0) {
    phones.push(...(foreignPhone.split(',').map(e => e.trim())))
  }
  if (foreignPhoneOthers.length > 0) {
    phones.push(...(foreignPhoneOthers.split(',').map(e => e.trim())))
  }
  if (chinesePhone.length > 0) {
    phones.push(...(chinesePhone.split(',').map(e => e.trim())))
  }
  await ref.doc(id).set({
    nameEn,
    nameRu,
    id,
    phones,
    useWhatsapp: useWhatsapp.trim() === '用',
    licenseImgToken,
    passportImgToken,
    recordId: record_id,
    updated: Timestamp.now()
  })
  console.log(`synced driver ${id}`);
  const res = await larkClient.updateRecord(larkClient.driverTableConfig, record_id, {
    'Firebase同步时间': (new Date()).getTime()
  });
  console.log(`updated driver ${JSON.stringify(res)}`);
  return {
    nameEn, id, passportImgToken, foreignPhone, chinesePhone, wechat
  };
}

async function syncTruck(truck, ref, larkClient) {
  const { fields, record_id } = truck
  const id = fields['车辆编号']
  const license1 = fields['车牌号1'] ? fields['车牌号1'][0].text : ''
  const license2 = fields['车牌号2'] ? fields['车牌号2'][0].text : ''
  const type = fields['车辆类型'] || ''
  const length = fields['车辆长度'] || ''
  const tonnage = fields['车载吨位'] || ''
  const unit = fields['装载位数'] || ''
  const volume = fields['车载体积'] || ''
  const driverId = fields['关联司机编号'] ? fields['关联司机编号'][0].text : ''
  const license1ImgToken = fields['车辆行驶证1'] ? fields['车辆行驶证1'][0].file_token : ''
  const license2ImgToken = fields['车辆行驶证2'] ? fields['车辆行驶证2'][0].file_token : ''
  const cCardImgToken = fields['C 卡'] ? fields['C 卡'][0].file_token : ''
  const greenCardImgToken = fields['海关绿卡'] ? fields['海关绿卡'][0].file_token : ''
  const returnTime = fields['司机回口岸时间'] ? Timestamp.fromMillis(fields['司机回口岸时间'].value[0]) : null

  console.log(id, license1, license2, type, length, tonnage, unit, volume, driverId);
  console.log(license1ImgToken, license2ImgToken, cCardImgToken, greenCardImgToken, returnTime);

  await ref.doc(id).set({
    license1,
    license2,
    id,
    type, length, tonnage, unit, volume, driverId,
    license1ImgToken, license2ImgToken, cCardImgToken, greenCardImgToken, returnTime,
    recordId: record_id,
    updated: Timestamp.now()
  })
  console.log(`synced truck ${id}`);
  const res = await larkClient.updateRecord(larkClient.truckTableConfig, record_id, {
    'Firebase同步时间': (new Date()).getTime()
  });
  console.log(`updated truck ${JSON.stringify(res)}`);
  return {
    id, license1, tonnage, type, volume
  };
}

async function sendNotification(firestore, uids, type, routeName, arguments, bodyFormatter) {
  try {
    const notif = (await remoteConfigAsJson('notifications'));
    if (notif == null) {
      return null;
    }

    let messages = {}
    const collection = firestore.collection('users');
    const setMessage = async (uid) => {
      const userDoc = await collection.doc(uid).get();
      if (!userDoc.exists) {
        return;
      }
      const userData = userDoc.data();
      const token = userData['fcm_token'];
      if (!token) {
        return;
      }

      const locale = userData['locale'] || 'zh';
      if (messages[locale] == null) {
        messages[locale] = {
          notification: {
            title: notif[type].title[locale],
            body: bodyFormatter == null ? notif[type].body[locale] : bodyFormatter(notif[type].body[locale])
          },
          data: {
            type,
            routeName: routeName || ''
          },
          tokens: [token],
          uids: [uid]
        };
        if (arguments != null) {
          messages[locale].data.arguments = arguments;
        }
      } else {
        messages[locale].tokens.push(token);
        messages[locale].uids.push(uid);
      }
    };

    for (const uid of uids) {
      await setMessage(uid);
    }

    if (Object.keys(messages).length == 0) {
      return null;
    }

    const results = await Promise.all(Object.values(messages).map(async (message) => {
      const tokens = [...message.tokens];
      const uids = [...message.uids];
      delete message.tokens;
      delete message.uids;
      if (tokens.length == 1) {
        message.token = tokens[0];
        const id = await getMessaging().send(message);
        console.log(`message sent ${id}`);

        await firestore.collection('notifications').add({
          message,
          created: Timestamp.now(),
          uid: uids[0],
          noficationId: id,
          unread: true
        });
        console.log(`message saved ${id}`);
        return id;
      } else {
        message.tokens = tokens;
        const resp = await getMessaging().sendEachForMulticast(message);
        console.log(`message sent ${resp.successCount}`);
        for (let i = 0; i < resp.responses.length; i++) {
          const r = resp.responses[i];
          if (r.success) {
            await firestore.collection('notifications').add({
              message,
              created: Timestamp.now(),
              uid: uids[i],
              noficationId: r.messageId,
              unread: true
            });
          }
        }
        console.log(`messages saved ${resp.successCount}`);
        return resp.successCount;
      }
    }));

    return results;
  } catch (err) {
    console.error(err);
    return null;
  }
}

async function remoteConfigAsJson(key) {
  try {
    const rc = getRemoteConfig();
    const value = (await rc.getServerTemplate()).toJSON().parameters[key].defaultValue.value;
    return JSON.parse(value);
  } catch (e) {
    console.log(e);
  }

  return null;
}

async function uploadImageToLark(storage, config, larkClient) {
  const filename = `/tmp/${path.basename(storage)}`
  const fileRef = getStorage().bucket().file(storage);
  await fileRef.download({
    destination: filename
  });
  try {
    const res = await larkClient.uploadFile(config, filename);
    return {
      "file_token": res.token,
      "name": res.name,
      "size": res.size,
      "type": "image/jpg"
    };
  } catch (e) {
    console.log(`${e}`);
  }
  return null;
}

module.exports = {
  bodyFromBasicAuthRequest,
  syncDriver,
  syncTruck,
  sendNotification,
  remoteConfigAsJson,
  uploadImageToLark
}